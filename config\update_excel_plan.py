#!/usr/bin/env python
# -*- coding: utf-8 -*-

import openpyxl
from openpyxl.styles import Alignment

def update_excel_plan(excel_file):
    """
    更新Excel文件中的第18周工作计划
    
    参数:
    excel_file -- Excel文件路径
    """
    try:
        print(f"正在读取文件: {excel_file}")
        
        # 打开Excel文件
        workbook = openpyxl.load_workbook(excel_file)
        
        # 获取第18周工作计划表
        sheet_name = "2025年第18周工作计划表"
        if sheet_name not in workbook.sheetnames:
            print(f"错误: 未找到工作表 '{sheet_name}'")
            return
        
        sheet = workbook[sheet_name]
        
        # 打印表头行，帮助调试
        print("正在查找表头行...")
        for row_idx in range(1, 11):
            cells = [cell.value for cell in sheet[row_idx] if isinstance(cell, openpyxl.cell.cell.Cell) and cell.value]
            print(f"行 {row_idx}: {cells}")
        
        # 第18周工作计划
        plans = [
            {
                "序号": 1,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述（提倡按照工作所属的模块来写，后期会根据大家提交的数据总结出固定的模块选项，比如硬件设计包含的工作模块有框图设计、原理图设计、器件采购申请、PCB设计这些）": "红外模块功能开发与测试",
                "工作性质": "改进迭代",
                "目标（提倡具体可衡量)": "完成红外模块状态机实现与优化",
                "重要性": "重要",
                "优先级": "高",
                "关键行动": "1. 实现红外模块状态机优化\n2. 解决资源释放问题\n3. 完善异常处理机制",
                "资源（人）": "段胜炜",
                "工时\n（h）": 10,
                "计划完成日期": "2025-04-29"
            },
            {
                "序号": 2,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述（提倡按照工作所属的模块来写，后期会根据大家提交的数据总结出固定的模块选项，比如硬件设计包含的工作模块有框图设计、原理图设计、器件采购申请、PCB设计这些）": "PRPD图谱显示问题修复",
                "工作性质": "缺陷修复",
                "目标（提倡具体可衡量)": "修复PRPD图谱显示的关键问题",
                "重要性": "重要",
                "优先级": "高",
                "关键行动": "1. 修复坐标轴标签错位问题\n2. 解决数据点颜色渐变问题\n3. 修复局部数据缺失问题",
                "资源（人）": "段胜炜",
                "工时\n（h）": 12,
                "计划完成日期": "2025-04-30"
            },
            {
                "序号": 3,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述（提倡按照工作所属的模块来写，后期会根据大家提交的数据总结出固定的模块选项，比如硬件设计包含的工作模块有框图设计、原理图设计、器件采购申请、PCB设计这些）": "固件包功能测试与完善",
                "工作性质": "测试验证",
                "目标（提倡具体可衡量)": "完成V4.5.0固件包功能回归测试",
                "重要性": "重要",
                "优先级": "高",
                "关键行动": "1. 执行固件包回归测试\n2. 编写自测报告\n3. 完成版本发布准备",
                "资源（人）": "段胜炜",
                "工时\n（h）": 8,
                "计划完成日期": "2025-04-28"
            },
            {
                "序号": 4,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述（提倡按照工作所属的模块来写，后期会根据大家提交的数据总结出固定的模块选项，比如硬件设计包含的工作模块有框图设计、原理图设计、器件采购申请、PCB设计这些）": "云平台HTTPS功能完善",
                "工作性质": "改进迭代",
                "目标（提倡具体可衡量)": "解决HTTPS登录功能稳定性问题",
                "重要性": "重要",
                "优先级": "中",
                "关键行动": "1. 修复SSL证书验证逻辑\n2. 完成证书链配置\n3. 实现长期解决方案",
                "资源（人）": "段胜炜",
                "工时\n（h）": 6,
                "计划完成日期": "2025-05-01"
            },
            {
                "序号": 5,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述（提倡按照工作所属的模块来写，后期会根据大家提交的数据总结出固定的模块选项，比如硬件设计包含的工作模块有框图设计、原理图设计、器件采购申请、PCB设计这些）": "蓝牙连接稳定性优化",
                "工作性质": "改进迭代",
                "目标（提倡具体可衡量)": "提高弱信号环境下蓝牙连接稳定性",
                "重要性": "重要",
                "优先级": "中",
                "关键行动": "1. 优化重试策略\n2. 实现信号强度检测\n3. 改进用户反馈机制",
                "资源（人）": "段胜炜",
                "工时\n（h）": 10,
                "计划完成日期": "2025-04-30"
            },
            {
                "序号": 6,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述（提倡按照工作所属的模块来写，后期会根据大家提交的数据总结出固定的模块选项，比如硬件设计包含的工作模块有框图设计、原理图设计、器件采购申请、PCB设计这些）": "系统集成测试",
                "工作性质": "测试验证",
                "目标（提倡具体可衡量)": "完成系统异常恢复能力测试",
                "重要性": "重要",
                "优先级": "中",
                "关键行动": "1. 执行异常恢复能力测试\n2. 修复高优先级集成问题\n3. 验证系统稳定性",
                "资源（人）": "段胜炜",
                "工时\n（h）": 8,
                "计划完成日期": "2025-04-29"
            },
            {
                "序号": 7,
                "项目名称": "管理",
                "类型": "管理项目",
                "小组": "嵌入式应用组",
                "工作概述（提倡按照工作所属的模块来写，后期会根据大家提交的数据总结出固定的模块选项，比如硬件设计包含的工作模块有框图设计、原理图设计、器件采购申请、PCB设计这些）": "技术评审会议",
                "工作性质": "沟通协调",
                "目标（提倡具体可衡量)": "确定问题优先级和解决方案",
                "重要性": "重要",
                "优先级": "高",
                "关键行动": "1. 召开技术评审会议\n2. 确定问题优先级\n3. 合理安排资源\n4. 确保核心功能按时交付",
                "资源（人）": "段胜炜",
                "工时\n（h）": 4,
                "计划完成日期": "2025-04-28"
            }
        ]
        
        # 查找表头行
        header_row = None
        for row_idx, row in enumerate(sheet.iter_rows(min_row=1, max_row=20), 1):
            if any(cell.value == "序号" for cell in row if isinstance(cell, openpyxl.cell.cell.Cell)):
                header_row = row_idx
                break
        
        if header_row is None:
            print("错误: 未找到表头行")
            return
        
        print(f"找到表头行: {header_row}")
        
        # 查找列索引
        columns = {}
        for cell in sheet[header_row]:
            if isinstance(cell, openpyxl.cell.cell.Cell) and cell.value:
                columns[cell.value] = cell.column
                print(f"列 '{cell.value}' 在位置 {cell.column}")
        
        # 检查必要的列是否存在
        required_columns = ["序号", "项目名称", "类型", "小组", "优先级", "关键行动", "计划完成日期"]
        missing_columns = [col for col in required_columns if col not in columns]
        if missing_columns:
            print(f"错误: 表格缺少以下列: {', '.join(missing_columns)}")
            return
        
        # 找到开始插入数据的行
        start_row = header_row + 1
        while start_row <= sheet.max_row:
            cell = sheet.cell(row=start_row, column=columns["序号"])
            if not cell.value:
                break
            start_row += 1
        
        print(f"将从行 {start_row} 开始插入数据")
        
        # 插入数据
        for i, plan in enumerate(plans, 0):
            row_idx = start_row + i
            
            # 写入数据
            for col_name, col_idx in columns.items():
                if col_name in plan:
                    cell = sheet.cell(row=row_idx, column=col_idx)
                    # 如果是合并单元格，则跳过写入
                    if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                        cell.value = plan[col_name]
                        if col_name == "关键行动":
                            # 设置换行格式
                            cell.alignment = Alignment(wrap_text=True, vertical="top")
        
        # 保存Excel文件
        workbook.save(excel_file)
        print(f"成功更新文件: {excel_file}")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    excel_file = r"File\20250425\QR-PDS-6.2-26WRPR-开发部-2025年第17周总结和2025年第18周计划-软件组.xlsx"
    update_excel_plan(excel_file) 