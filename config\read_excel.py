import pandas as pd
import os

# 获取Excel文件的路径
excel_file_path = "File/20250425/QR-PDS-6.2-26WRPR-开发部-2025年第17周总结和2025年第18周计划-软件组.xlsx"

# 检查文件是否存在
if not os.path.exists(excel_file_path):
    print(f"文件不存在: {excel_file_path}")
    exit(1)

# 读取Excel文件中的所有工作表
try:
    # 获取所有工作表名称
    xlsx = pd.ExcelFile(excel_file_path)
    sheet_names = xlsx.sheet_names
    print(f"Excel文件中的工作表: {sheet_names}")
    
    # 读取每个工作表的内容
    for sheet_name in sheet_names:
        print(f"\n工作表: {sheet_name}")
        df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
        print(df.head())
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print(f"列名: {df.columns.tolist()}")
except Exception as e:
    print(f"读取Excel文件时出错: {e}") 