import pandas as pd
from openpyxl import load_workbook
from datetime import datetime

# 工作计划内容
week18_plans = [
    {
        "序号": 1.0,
        "项目名称": "PDS-Z200_T95",
        "类型": "产品项目",
        "小组": "嵌入式应用组",
        "工作概述": "T95手持终端固件开发-实现UHF数据解析功能",
        "工作性质": "新开发",
        "目标": "完成UHF数据接收和解析模块，支持PRPD图形显示",
        "重要性": "重要",
        "优先级": "紧急",
        "关键行动": "1.UHF数据协议分析\n2.数据解析模块实现\n3.图形显示调试",
        "资源（人）": "张三",
        "工时": 14,
        "计划完成日期": "2025-04-30",
        "备注": ""
    },
    {
        "序号": 2.0,
        "项目名称": "PDS-Z200_T95",
        "类型": "产品项目",
        "小组": "嵌入式应用组",
        "工作概述": "T95手持终端蓝牙通信稳定性优化",
        "工作性质": "改进迭代",
        "目标": "提高蓝牙连接的稳定性和抗干扰能力",
        "重要性": "重要",
        "优先级": "紧急",
        "关键行动": "1.分析当前蓝牙连接断开原因\n2.优化蓝牙重连机制\n3.测试连接稳定性",
        "资源（人）": "李四",
        "工时": 10,
        "计划完成日期": "2025-04-29",
        "备注": ""
    },
    {
        "序号": 3.0,
        "项目名称": "上海电缆数字化项目",
        "类型": "工程项目",
        "小组": "服务端",
        "工作概述": "API接口优化及文档更新",
        "工作性质": "改进迭代",
        "目标": "完善API接口文档，优化接口响应速度",
        "重要性": "重要",
        "优先级": "普通",
        "关键行动": "1.API响应优化\n2.更新接口文档\n3.进行性能测试",
        "资源（人）": "王五",
        "工时": 8,
        "计划完成日期": "2025-05-01",
        "备注": ""
    },
    {
        "序号": 4.0,
        "项目名称": "PDS-Z200_T95",
        "类型": "产品项目",
        "小组": "嵌入式应用组",
        "工作概述": "云平台连接功能测试",
        "工作性质": "测试验证",
        "目标": "验证设备与云平台的连接和数据传输功能",
        "重要性": "重要",
        "优先级": "普通",
        "关键行动": "1.配置云平台连接参数\n2.测试数据上传功能\n3.验证数据展示正确性",
        "资源（人）": "张三",
        "工时": 8,
        "计划完成日期": "2025-05-02",
        "备注": ""
    },
    {
        "序号": 5.0,
        "项目名称": "数据集建设",
        "类型": "产品项目",
        "小组": "服务端",
        "工作概述": "缺陷数据标注及处理",
        "工作性质": "新开发",
        "目标": "完成500张图像的缺陷标注和分类",
        "重要性": "一般",
        "优先级": "普通",
        "关键行动": "1.图像预处理\n2.标注缺陷区域\n3.归类整理数据",
        "资源（人）": "赵六",
        "工时": 12,
        "计划完成日期": "2025-05-03",
        "备注": ""
    }
]

# Excel文件路径
excel_file = '工作汇报.xlsx'  # 请修改为实际的Excel文件名

try:
    # 加载工作簿
    workbook = load_workbook(excel_file)
    
    # 选择工作表
    sheet_name = '2025年第18周工作计划表'
    if sheet_name in workbook.sheetnames:
        sheet = workbook[sheet_name]
        
        # 获取列名和它们的索引
        header_row = 5  # 假设表头在第5行
        
        # 清除现有内容（从第6行开始，保留表头）
        for row in range(6, sheet.max_row + 1):
            for col in range(1, sheet.max_column + 1):
                sheet.cell(row=row, column=col).value = None
        
        # 添加新的计划内容
        start_row = 6  # 从第6行开始添加数据
        
        for i, plan in enumerate(week18_plans):
            row_idx = start_row + i
            
            # 设置单元格值
            sheet.cell(row=row_idx, column=1).value = plan["序号"]
            sheet.cell(row=row_idx, column=2).value = plan["项目名称"]
            sheet.cell(row=row_idx, column=3).value = plan["类型"]
            sheet.cell(row=row_idx, column=4).value = plan["小组"]
            sheet.cell(row=row_idx, column=5).value = plan["工作概述"]
            sheet.cell(row=row_idx, column=6).value = plan["工作性质"]
            sheet.cell(row=row_idx, column=7).value = plan["目标"]
            sheet.cell(row=row_idx, column=8).value = plan["重要性"]
            sheet.cell(row=row_idx, column=9).value = plan["优先级"]
            sheet.cell(row=row_idx, column=10).value = plan["关键行动"]
            sheet.cell(row=row_idx, column=11).value = plan["资源（人）"]
            sheet.cell(row=row_idx, column=12).value = plan["工时"]
            
            # 处理日期
            if "计划完成日期" in plan and plan["计划完成日期"]:
                try:
                    date_obj = datetime.strptime(plan["计划完成日期"], "%Y-%m-%d")
                    sheet.cell(row=row_idx, column=13).value = date_obj
                except ValueError:
                    sheet.cell(row=row_idx, column=13).value = plan["计划完成日期"]
            
            sheet.cell(row=row_idx, column=14).value = plan["备注"] if "备注" in plan else ""
            
        # 保存工作簿
        workbook.save(excel_file)
        print(f"成功更新'{sheet_name}'工作表！")
    else:
        print(f"工作表'{sheet_name}'不存在！")
        
except Exception as e:
    print(f"更新Excel文件时发生错误: {e}") 