# T95图谱数据文件结构规范

## 文档版本

| 版本号 | 日期 | 修改人 | 修改内容 |
|--------|------|--------|----------|
| v1.0 | 2025-04-22 | 技术开发部 | 初始版本 |

## 概述

本文档详细描述T95手持终端中各类图谱数据的文件结构、格式规范、数据组织方式和字段定义。本规范适用于T95手持终端开发和测试团队，作为图谱数据处理的统一标准参考。

## 1. 通用文件结构

所有图谱数据文件遵循以下通用结构：

```
+---------------+
| 文件头 (64字节) |
+---------------+
| 图谱类型标识    |
+---------------+
| 数据区         |
+---------------+
| 校验区         |
+---------------+
```

### 1.1 文件头结构

| 偏移量 | 字节数 | 字段名称 | 数据类型 | 说明 |
|--------|--------|----------|----------|------|
| 0 | 8 | 文件标识 | char[8] | 固定值 "T95DATA" |
| 8 | 4 | 版本号 | uint32 | 当前为 0x00010000 (v1.0) |
| 12 | 4 | 图谱类型 | uint32 | 具体类型标识见下文 |
| 16 | 8 | 创建时间 | uint64 | Unix时间戳(毫秒) |
| 24 | 8 | 数据大小 | uint64 | 数据区字节数 |
| 32 | 16 | 设备ID | char[16] | 采集设备标识 |
| 48 | 4 | 保留字段1 | uint32 | 保留，填0 |
| 52 | 4 | 保留字段2 | uint32 | 保留，填0 |
| 56 | 4 | 保留字段3 | uint32 | 保留，填0 |
| 60 | 4 | 头部校验和 | uint32 | CRC32校验值 |

### 1.2 图谱类型标识

| 类型代码 | 说明 |
|----------|------|
| 0x01 | PRPD图谱 |
| 0x02 | PRPS图谱 |
| 0x03 | UHF图谱 |
| 0x04 | TEV图谱 |
| 0x05 | AE图谱 |

## 2. PRPD图谱数据格式

相位分辨局部放电图谱(Phase Resolved Partial Discharge)展示了局部放电幅值与相位的关系。

### 2.1 数据结构

PRPD图谱数据区结构：

```
+----------------------+
| PRPD头部 (24字节)     |
+----------------------+
| 相位-幅值数据表        |
+----------------------+
```

### 2.2 PRPD头部结构

| 偏移量 | 字节数 | 字段名称 | 数据类型 | 说明 |
|--------|--------|----------|----------|------|
| 0 | 4 | 幅值最大值 | float | 幅值范围上限 |
| 4 | 4 | 幅值最小值 | float | 幅值范围下限 |
| 8 | 4 | 相位分辨率 | uint32 | 单位: 度/点 |
| 12 | 4 | 相位点数 | uint32 | 横轴数据点数量 |
| 16 | 4 | 幅值分辨率 | uint32 | 单位: mV/点 |
| 20 | 4 | 幅值点数 | uint32 | 纵轴数据点数量 |

### 2.3 相位-幅值数据表

数据表为二维矩阵，大小为 `相位点数×幅值点数`，每个元素为uint32类型，表示该位置处的放电次数。

## 3. PRPS图谱数据格式

相位分辨脉冲序列图谱(Phase Resolved Pulse Sequence)展示了局部放电幅值与脉冲序列的关系。

### 3.1 数据结构

PRPS图谱数据区结构：

```
+----------------------+
| PRPS头部 (28字节)     |
+----------------------+
| 脉冲序列数据           |
+----------------------+
```

### 3.2 PRPS头部结构

| 偏移量 | 字节数 | 字段名称 | 数据类型 | 说明 |
|--------|--------|----------|----------|------|
| 0 | 4 | 幅值最大值 | float | 幅值范围上限 |
| 4 | 4 | 幅值最小值 | float | 幅值范围下限 |
| 8 | 4 | 相位周期数 | uint32 | 记录的电源周期数 |
| 12 | 4 | 采样率 | uint32 | 单位: 采样点/周期 |
| 16 | 4 | 脉冲总数 | uint32 | 记录的脉冲总数 |
| 20 | 4 | 脉冲最大值 | float | 最大脉冲幅值 |
| 24 | 4 | 脉冲最小值 | float | 最小脉冲幅值 |

### 3.3 脉冲序列数据

脉冲序列数据为变长结构，每个脉冲数据由以下字段组成：

| 字节数 | 字段名称 | 数据类型 | 说明 |
|--------|----------|----------|------|
| 4 | 脉冲幅值 | float | 脉冲的幅值 |
| 4 | 相位位置 | float | 脉冲的相位位置(0-360度) |
| 4 | 时间戳 | uint32 | 脉冲的时间戳(相对值) |

## 4. UHF图谱数据格式

超高频(Ultra High Frequency)图谱展示了不同频率范围内的信号强度。

### 4.1 数据结构

UHF图谱数据区结构：

```
+----------------------+
| UHF头部 (24字节)      |
+----------------------+
| 频谱数据              |
+----------------------+
```

### 4.2 UHF头部结构

| 偏移量 | 字节数 | 字段名称 | 数据类型 | 说明 |
|--------|--------|----------|----------|------|
| 0 | 4 | 起始频率 | float | 单位: MHz |
| 4 | 4 | 终止频率 | float | 单位: MHz |
| 8 | 4 | 频率点数 | uint32 | 频谱数据点数量 |
| 12 | 4 | 最大强度 | float | 信号最大强度 |
| 16 | 4 | 最小强度 | float | 信号最小强度 |
| 20 | 4 | 平均次数 | uint32 | 谱图平均次数 |

### 4.3 频谱数据

频谱数据为一维数组，大小为`频率点数`，每个元素为float类型，表示对应频率点的信号强度。

## 5. 文件校验机制

### 5.1 CRC32校验

文件头校验和使用CRC32算法计算。计算范围为文件头的前60个字节。

### 5.2 数据完整性校验

数据区结束后，附加4字节的CRC32校验和，用于验证数据完整性。计算范围为整个数据区。

## 6. 数据转换规则

### 6.1 PRPS转PRPD

将PRPS数据转换为PRPD数据时，应遵循以下规则：

1. 根据PRPS头部中的相位周期数和采样率，确定PRPD的相位分辨率
2. 根据PRPS中记录的脉冲幅值范围，确定PRPD的幅值分辨率
3. 对每个脉冲数据，根据其相位位置和幅值，在PRPD二维矩阵中对应位置加1
4. 相位计算采用精确浮点数值，转换为矩阵索引时使用四舍五入方式
5. 幅值归一化时确保不会丢失有效数据点

## 7. 文件命名规范

图谱数据文件应按以下格式命名：

```
T95_<图谱类型>_<设备ID>_<采集时间>.gph
```

其中：
- `<图谱类型>`: PRPD、PRPS、UHF、TEV或AE
- `<设备ID>`: 采集设备的唯一标识
- `<采集时间>`: 格式为YYYYMMDD_HHMMSS

## 8. 注意事项

1. 所有多字节数据采用小端序(Little Endian)存储
2. 浮点数据采用IEEE 754标准的32位单精度浮点格式
3. 所有保留字段必须填充为0
4. 图谱类型标识必须与数据内容一致
5. 文件头校验和必须正确，否则文件将被视为无效
6. 数据处理时应当验证数据完整性校验和

## 9. 开发建议

1. 使用标准库函数计算CRC32校验和，确保一致性
2. 文件读取时应先验证文件头的有效性
3. 实现数据转换功能时注意处理边界条件和异常情况
4. 图谱显示时应根据实际需求调整分辨率和显示范围
5. 处理大文件时建议采用数据分块和缓存机制 