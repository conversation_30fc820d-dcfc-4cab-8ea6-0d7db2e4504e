from datetime import datetime
from zoneinfo import ZoneInfo
import sys

# 获取本地时间并转换为上海时间
def get_local_shanghai_time():
    try:
        shanghai_tz = ZoneInfo('Asia/Shanghai')
        shanghai_time = datetime.now(shanghai_tz)
        return shanghai_time
    except Exception as e:
        print(f"获取上海时间时出错: {e}")
        return datetime.now()

# 返回指定格式的上海时间字符串
def get_formatted_shanghai_time(format_str='%Y-%m-%d %H:%M:%S'):
    return get_local_shanghai_time().strftime(format_str)

# 打印当前上海时间
if __name__ == "__main__":
    time_format = '%Y-%m-%d %H:%M:%S'
    if len(sys.argv) > 1:
        time_format = sys.argv[1]
    print(get_formatted_shanghai_time(time_format)) 