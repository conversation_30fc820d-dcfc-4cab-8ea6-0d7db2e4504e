# T95 GuideInfrared 红外功能代码分析文档

## 1. 目录结构与文件说明

```
Z200/view/guideinfrared/
├── guideinfraredviewbase.h/cpp   // 红外引导视图基类，核心逻辑
├── guideinfraredview.h/cpp       // 红外引导视图派生类，UI与业务结合
├── guideinfraredplaybackview.h/cpp // Guide红外数据回放
├── 其它相关头文件/实现文件
infrared/guide/
├── guideclientmanager.h/cpp      // Guide红外客户端管理器，底层通信
infrared/
├── guideinfrareddatasave.h/cpp   // Guide红外数据保存
dataSave/
├── DataFileInfos.h/cpp           // 数据文件信息结构体
deleteDataView/
├── RotateDeleteDataView.h/cpp    // 数据删除视图
其它：config、systemsetting、statusbar、log、fileoper等
```

- **guideinfraredviewbase.h/cpp**  
  封装了红外引导模式下的通用显示、数据处理、模式切换等基础功能，是其它红外引导视图的基类。
- **guideinfraredview.h/cpp**  
  继承自`guideinfraredviewbase`，实现具体的UI交互、信号槽连接、与主界面联动等。
- **guideinfraredplaybackview.h/cpp**  
  负责历史数据的回放、浏览、交互操作。

## 2. 主要类与职责

### 2.1 GuideInfraredViewBase

#### 主要职责
- 管理红外显示模式（如红外、可见光、融合等）
- 提供显示模式切换接口（如`switchDisplayMode(DisplayMode mode)`）
- 处理与GuideClientManager的交互（如数据获取、模式同步）
- 负责信号发射（如`displayModeChanged(DisplayMode mode)`）

#### 关键成员函数
```cpp
/**
 * @brief 切换红外显示模式
 * @param mode 目标显示模式
 * @return 切换是否成功
 */
virtual bool switchDisplayMode(DisplayMode mode);
```
- 内部会与GuideClientManager通信，完成底层模式切换，并发射`displayModeChanged`信号。

#### 典型信号
```cpp
signals:
    void displayModeChanged(DisplayMode mode); ///< 显示模式切换信号
```

### 2.2 GuideInfraredView

#### 主要职责
- 继承自GuideInfraredViewBase，负责UI层面的交互
- 响应用户操作（如按钮点击），调用基类接口切换显示模式
- 处理显示模式切换后的UI刷新

#### 关键成员函数
```cpp
/**
 * @brief 响应显示模式切换按钮点击
 */
void onDisplayModeButtonClicked();

/**
 * @brief 显示模式切换信号响应槽
 * @param mode 新的显示模式
 */
void onDisplayModeChanged(DisplayMode mode);
```
- `onDisplayModeButtonClicked()`：通常与UI按钮绑定，调用`switchDisplayMode`实现模式切换。
- `onDisplayModeChanged()`：刷新界面，适配不同显示模式下的UI状态。

## 3. 关键流程与交互

### 3.1 显示模式切换流程

1. 用户点击"切换显示模式"按钮
2. `GuideInfraredView::onDisplayModeButtonClicked()`被触发
3. 调用`switchDisplayMode(nextMode)`，由基类处理底层切换
4. 切换成功后，基类发射`displayModeChanged(mode)`信号
5. `GuideInfraredView::onDisplayModeChanged(mode)`槽函数响应，刷新UI

#### 伪代码流程
```cpp
void GuideInfraredView::onDisplayModeButtonClicked() {
    DisplayMode nextMode = ...; // 计算下一个模式
    switchDisplayMode(nextMode);
}

bool GuideInfraredViewBase::switchDisplayMode(DisplayMode mode) {
    // 与GuideClientManager通信
    // 切换成功 emit displayModeChanged(mode);
}
```

### 3.2 与GuideClientManager的关系

- GuideClientManager负责与底层硬件/服务通信，GuideInfraredViewBase通过它实现数据和模式的同步。
- 典型调用如`GuideClientManager::instance()->switchDisplayMode(mode)`。

## 4. 常见易错点与建议

- **信号未声明**：如`displayModeChanged`信号必须在基类signals区声明，否则emit时编译报错。
- **按钮信息未定义**：如`g_SwitchModeButtonInfo`需在相关头/源文件中定义并初始化。
- **接口命名不一致**：如GuideClientManager应使用`instance()`而非`getInstance()`。
- **switch语句未覆盖所有枚举值**：建议加default分支或专门处理所有枚举，消除编译警告。

## 5. 代码示例（带Doxygen注释）

```cpp
/**
 * @brief 切换红外显示模式
 * @param mode 目标显示模式
 * @return 切换是否成功
 */
virtual bool switchDisplayMode(DisplayMode mode);

/**
 * @brief 显示模式切换信号
 * @param mode 新的显示模式
 */
signals:
    void displayModeChanged(DisplayMode mode);
```

## 6. UI交互建议

- 建议将"切换显示模式"按钮加入按钮栏，并在按钮响应函数中调用`onDisplayModeButtonClicked()`，实现与业务逻辑的解耦。
- UI刷新逻辑应集中在`onDisplayModeChanged`槽函数中，便于维护和扩展。

## 7. 总结

`Z200/view/guideinfrared`目录下的红外相关代码采用了"基类+派生类"分层设计，基类负责核心业务逻辑，派生类负责UI交互。
显示模式切换流程清晰，信号槽机制完善，便于后续功能扩展和维护。
建议开发过程中注意接口一致性、信号声明完整性以及UI与业务逻辑的解耦。

## 8. guideinfraredview.cpp头文件依赖与模块交互分析

### 8.1 头文件引用说明

- `<QtConcurrentRun>`：Qt并发库，异步任务处理（如数据保存）
- `dataSave/DataFileInfos.h`：数据文件信息结构体，数据保存相关
- `deleteDataView/RotateDeleteDataView.h`：数据删除视图，支持数据管理
- `infrared/guideinfrareddatasave.h`：Guide红外数据保存实现
- `infrared/rotatewidget.h`：旋转控件，UI交互
- `guideinfraredplaybackview.h`：Guide红外数据回放视图
- `guideinfraredview.h`：本类头文件，类声明
- `config/ConfigManager.h`：配置管理，参数读取与保存
- `systemsetting/SystemSet.h`：系统设置，温度单位等参数
- `statusbar/StatusBar.h`：状态栏，界面提示
- `appconfig.h`：应用配置常量
- `log/log.h`：日志记录
- `fileoper/fileoperutil.h`：文件操作工具
- `infrared/guide/guideclientmanager.h`：Guide红外客户端管理器，底层通信

### 8.2 依赖关系与模块交互

- **QtConcurrentRun** 用于异步保存数据，提升UI响应性。配合`QFutureWatcher`监控保存任务完成。
- **数据保存与管理**：`DataFileInfos.h`、`guideinfrareddatasave.h`定义数据结构和保存实现，`fileoperutil.h`支持文件操作，`RotateDeleteDataView.h`提供数据删除界面。
- **UI与交互**：`rotatewidget.h`提升交互体验，`guideinfraredplaybackview.h`支持历史数据回放，`statusbar.h`用于状态提示，`appconfig.h`、`systemsetting.h`、`configmanager.h`统一管理参数。
- **日志与调试**：`log.h`记录关键操作和异常。
- **GuideClientManager**：`guideclientmanager.h`为核心通信枢纽，负责与底层硬件/服务通信，数据采集、模式切换、设备状态监控等。

### 8.3 典型交互流程举例

- **数据采集与显示**：GuideInfraredView通过GuideClientManager注册回调，接收红外数据（如sigRgbData、sigY16Data），数据到达后调用成像视图显示。
- **数据保存**：用户点击保存按钮，触发saveData，通过QtConcurrentRun异步调用数据保存函数，保存完成后通过QFutureWatcher发射信号，UI层响应。
- **数据回放与删除**：用户通过按钮栏进入回放或删除界面，分别调用GuideInfraredPlaybackView或RotateDeleteDataView进行历史数据操作。
- **显示模式切换**：用户操作按钮栏，触发显示模式切换，通过GuideClientManager与底层通信，切换成功后发射displayModeChanged信号，UI层刷新。

### 8.4 易错点与建议

- 头文件引用较多，需确保各模块接口清晰、依赖合理，避免循环依赖。
- GuideClientManager为核心通信枢纽，接口变更需同步更新相关调用。
- 异步保存需注意线程安全和UI同步，避免资源竞争。
- 配置与参数管理建议集中处理，避免分散在各子模块。

## 9. GuideInfrared 红外模块完整实现解读

### 9.1 模块结构与文件分布

```
Z200/view/guideinfrared/
├── guideinfraredviewbase.h/cpp   // Guide红外视图基类，核心业务逻辑
├── guideinfraredview.h/cpp       // Guide红外视图派生类，UI与业务结合
├── guideinfraredplaybackview.h/cpp // Guide红外数据回放
├── 其它相关头文件/实现文件
infrared/guide/
├── guideclientmanager.h/cpp      // Guide红外客户端管理器，底层通信
infrared/
├── guideinfrareddatasave.h/cpp   // Guide红外数据保存
dataSave/
├── DataFileInfos.h/cpp           // 数据文件信息结构体
deleteDataView/
├── RotateDeleteDataView.h/cpp    // 数据删除视图
其它：config、systemsetting、statusbar、log、fileoper等
```

---

### 9.2 主要类与职责（源码级解读）

#### GuideInfraredViewBase

```cpp
/*!\brief Guide红外视图基类，负责红外数据采集、显示模式切换、信号发射等核心逻辑 */
class GuideInfraredViewBase : public QWidget {
    Q_OBJECT
public:
    GuideInfraredViewBase(QWidget* parent = nullptr);
    virtual ~GuideInfraredViewBase();
    virtual bool switchDisplayMode(DisplayMode mode);
    // 其它如采集、暂停、恢复、保存等接口...
signals:
    void displayModeChanged(DisplayMode mode);
protected:
    DisplayMode m_eGuideInfraredDisplayMode; ///< 当前显示模式
    // 其它成员变量...
};
```
**实现说明：**
- 管理红外显示模式、数据采集、暂停、恢复、保存等核心业务。
- 通过GuideClientManager与底层硬件/服务通信。
- 切换显示模式时，emit displayModeChanged(mode)信号，通知UI层刷新。

#### GuideInfraredView

```cpp
/*!\brief Guide红外视图派生类，负责UI交互与业务结合 */
class GuideInfraredView : public GuideInfraredViewBase {
    Q_OBJECT
public:
    GuideInfraredView(QWidget* parent = nullptr);
    virtual ~GuideInfraredView();
    void onDisplayModeButtonClicked();
    void onDisplayModeChanged(DisplayMode mode);
    void onButtonPressed(UINT8 ucID);
    void saveData(bool bSaveJpeg);
    // 其它UI相关槽函数...
protected:
    // UI控件、状态变量等
};
```
**实现说明：**
- 负责UI层面的交互，响应按钮栏操作，调用基类接口。
- 处理信号槽，刷新界面。
- 通过按钮栏分发，统一处理各类业务操作。

#### GuideClientManager

```cpp
/*!\brief Guide红外客户端管理器，负责与底层硬件/服务通信 */
class GuideClientManager : public QObject {
    Q_OBJECT
public:
    static GuideClientManager* instance();
    bool switchDisplayMode(DisplayMode mode);
    // 其它如采集、停止、参数设置等接口...
signals:
    void sigRgbData(QSharedPointer<Guide::GuideRGBDataInfo>);
    void sigY16Data(QSharedPointer<Guide::GuideY16DataInfo>);
    void sigConnectInfraredDevFinished(bool);
    // 其它信号...
};
```
**实现说明：**
- 单例模式，统一管理与底层硬件/服务的通信。
- 负责数据采集、显示模式切换、设备状态监控等。
- 通过信号通知上层数据到达、设备状态变化等。

#### 其它关键类
- **GuideInfraredDataSave**：负责Guide红外数据的本地保存，支持异步保存、格式转换等。
- **GuideInfraredPlaybackView**：负责历史数据的回放、浏览、交互操作。
- **RotateDeleteDataView**：数据删除界面，支持批量管理。
- **ConfigManager/SystemSet**：参数管理与配置。
- **日志、文件操作、状态栏等辅助类**。

### 9.3 关键成员函数详细说明

#### switchDisplayMode
```cpp
/*!\brief 切换红外显示模式 */
bool GuideInfraredViewBase::switchDisplayMode(DisplayMode mode) {
    // 1. 调用GuideClientManager::instance()->switchDisplayMode(mode)
    // 2. 切换成功则更新m_eGuideInfraredDisplayMode
    // 3. emit displayModeChanged(mode)
    // 4. 返回切换结果
}
```

#### saveData
```cpp
/*!\brief 保存红外数据 */
void GuideInfraredView::saveData(bool bSaveJpeg) {
    // 1. 采集当前数据，封装为GuideInfraredDataInfo
    // 2. QtConcurrentRun异步调用GuideInfraredDataSave::saveData
    // 3. QFutureWatcher监控保存完成，emit sigSaveFinished()
    // 4. UI层响应保存结果
}
```

#### onButtonPressed
```cpp
/*!\brief 按钮栏统一响应函数 */
void GuideInfraredView::onButtonPressed(UINT8 ucID) {
    // switch(ucID)分发到采集、保存、回放、删除、切换模式等具体业务
}
```

### 9.4 典型业务流程（时序图/流程图）

#### 采集-显示-保存-回放流程时序图

```mermaid
sequenceDiagram
    participant User
    participant GuideInfraredView
    participant GuideInfraredViewBase
    participant GuideClientManager
    participant GuideInfraredImagingView
    participant GuideInfraredDataSave

    User->>GuideInfraredView: 点击"开始采集"
    GuideInfraredView->>GuideInfraredViewBase: startReadData()
    GuideInfraredViewBase->>GuideClientManager: 请求采集数据
    GuideClientManager-->>GuideInfraredViewBase: 返回数据
    GuideInfraredViewBase->>GuideInfraredView: emit dataReady()
    GuideInfraredView->>GuideInfraredImagingView: setInfraredData()
    User->>GuideInfraredView: 点击"保存"
    GuideInfraredView->>GuideInfraredViewBase: saveData()
    GuideInfraredViewBase->>GuideInfraredDataSave: 保存数据
```

#### 显示模式切换流程

```mermaid
sequenceDiagram
    participant User
    participant GuideInfraredView
    participant GuideInfraredViewBase
    participant GuideClientManager

    User->>GuideInfraredView: 点击"切换显示模式"
    GuideInfraredView->>GuideInfraredViewBase: switchDisplayMode(nextMode)
    GuideInfraredViewBase->>GuideClientManager: switchDisplayMode(nextMode)
    GuideClientManager-->>GuideInfraredViewBase: 切换结果
    GuideInfraredViewBase->>GuideInfraredView: emit displayModeChanged(nextMode)
    GuideInfraredView->>GuideInfraredView: 刷新UI
```

### 9.5 UML类图

```mermaid
classDiagram
    class GuideInfraredViewBase {
        +switchDisplayMode(mode)
        +displayModeChanged(mode)
        -m_eGuideInfraredDisplayMode
    }
    class GuideInfraredView {
        +onDisplayModeButtonClicked()
        +onDisplayModeChanged(mode)
        +onButtonPressed(ucID)
        +saveData(bSaveJpeg)
    }
    class GuideClientManager {
        +instance()
        +switchDisplayMode(mode)
        +sigRgbData
        +sigY16Data
    }
    GuideInfraredViewBase <|-- GuideInfraredView
    GuideInfraredViewBase --> GuideClientManager
    GuideInfraredView --> GuideInfraredImagingView
    GuideInfraredView --> GuideInfraredDataSave
```

### 9.6 信号槽与模块交互说明

- **数据流**：GuideClientManager采集到数据后，通过信号sigRgbData/sigY16Data通知GuideInfraredView，GuideInfraredView再传递给成像视图显示。
- **模式切换**：UI按钮触发onDisplayModeButtonClicked，调用switchDisplayMode，底层切换后emit displayModeChanged，UI层刷新。
- **数据保存**：saveData异步保存，完成后emit sigSaveFinished，UI层弹窗提示。
- **回放/删除**：通过GuideInfraredPlaybackView/RotateDeleteDataView实现，支持历史数据管理。

### 9.7 易错点与开发建议

- **信号未声明或未正确connect**，导致UI不刷新。
- **按钮信息未定义**，按钮栏异常。
- **枚举未覆盖所有分支**，编译警告。
- **GuideClientManager接口变更未同步**，导致切换失败。
- **异步保存需注意线程安全和UI同步**。
- **配置与参数管理建议集中处理**，避免分散在各子模块。

### 9.8 总结与扩展建议

GuideInfrared红外模块采用分层设计，核心逻辑与UI解耦，信号槽机制完善，便于扩展和维护。
建议开发时注重接口一致性、信号完整性、UI与业务分离、数据流清晰。
如需进一步细化某一子模块（如数据保存、成像、回放、参数设置等），或需要具体UML/流程图，请随时告知！ 