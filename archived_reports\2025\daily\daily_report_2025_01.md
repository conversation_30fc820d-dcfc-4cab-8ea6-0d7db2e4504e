# 每日工作记录

## 日期：2025-01-20

1. 完成江苏数字化合并方案相关材料准备 [管理]
   - 工时：2小时
   - 整理评审相关材料
   - 拉会参与讨论相关合并方案

2. 梳理T95江苏数字化接入终端功能点和需要修改的UI设计 [设计]
   - 工时：2小时

3. 分析现场T95连接失败原因 [维护]
   - 工时：4小时

4. 协助测试完成T95相关测试 [测试]
   - 工时：1小时

5. 和同事沟通PRPS数据转PRPD数据公式 [沟通]
   - 工时：1小时

   
## 日期：2025-01-21

1. 分析20250120现场日志，排查北京现场连终端提示连接失败问题 [维护]
   - 工时：2小时

2. 复现现场连接失败场景，通过公司的荣耀手机和自己的小米手机验证排查 [测试]
   - 工时：3小时

3. 梳理绘制T95蓝牙连接流程图 [设计]
   - 工时：1小时

4. 输出北京现场蓝牙连接失败分析文档 [文档]
   - 工时：1小时

5. 分析20250121现场日志，排查北京现场T95上传数据显示成功但平台未显示数据问题 [维护]
   - 工时：2小时

6. 分析现场反馈日志和解析指定存在问题的子任务数据报文 [分析]
   - 工时：2小时

7. 输出北京现场上传成功未展示数据分析文档 [文档]
   - 工时：3小时

## 日期：2025-01-22

1. 整理T95日志蓝牙报文分析工具 [编码]
   - 工时：2小时
   - 目前只需将T95需要解析的T95日志文件内容按照工具定义的规则，则可以很快解析出报文信息和xml文件以及数据文件。

2. 排查北京现场反馈日志 [维护]
   - 工时：3小时
   - 分析重新提交该子任务之后，发现出现一个未测的测点。并且任务变为了未测问题。
   - 分析下发单个测点，子任务列表未刷新问题。

3. 整理月度绩效考核表 [管理]
   - 工时：1小时

4. 梳理下个月工作计划内容 [设计]
   - 工时：1小时

5. 和杨洪波沟通北京现场反馈的问题和从日志中分析发现的内容 [管理]
   - 工时：1小时

6. 参与Cursor分享会，学习使用AI完成开发工作 [学习]
   - 工时：2.5小时

## 日期：2025-01-23

1. 排查北京现场反馈日志 [维护]
   - 工时：6小时
   - 分析移动终端下发单个测点任务，该子任务已存在的情况下，T95的子任务列表刷新页面，但是未改变该子任务状态（已测、未测）问题。
   - 整理编辑北京项目现场测试问题分析报告。

2. 排查T95蓝牙连接失败原因 [维护]
   - 工时：4小时
   - 用泰岳设备进行测试，未发现问题。
   - 查看代码梳理流程，输出问题排查分析报告。

3. 梳理T95江苏数字化接入终端功能点和需要修改的UI设计 [设计]
   - 工时：2小时

4. 编辑江苏数字化合并方案文档 [设计]
   - 工时：3小时

## 日期：2025-01-24

1. 蓝牙连接失败原因分析 [维护]
   - 工时：7小时
   - 主要原因：没有及时对信号量 `m_pSemServ` 在 `m_bServiceState` 赋值后立即释放，可能导致状态更新不及时。
   - 排查过程：通过分析日志和信号量的使用，发现问题所在。
   - 验证分析：进行了多次测试验证，确认问题原因。
   - 后续计划：计划优化信号量的使用，确保状态及时更新。

2. 测点合并方案 [设计]
   - 工时：1小时
   - 方案评审：如何高效地将补测或新测点的数据合并到主任务进行功能评审。
   - 问题发现：合并后没有刷新界面，导致任务显示状态可能不正确。
   - 数据校验：文件合并后需校验任务和测点数据，但大数据量下校验和遍历可能会增加耗时。

3. 江苏数字化合并方案评审 [管理]
   - 工时：1小时
   - 功能保留：保留原有江苏功能，不对其进行功能迭代，保持原有设计。
   - 代码处理：通信模块业务代码进行单独处理，不影响主分支代码。

## 日期：2025-01-25

1. 蓝牙连接问题分析 [维护]
   - 工时：1小时
   - 问题分析：分析了蓝牙连接过程中出现的 "RemoteDeviceServices is invalid" 错误方向，检查了相关函数和信号量的使用，找出了问题的根源。

2. 代码审查与日志添加 [维护]
   - 工时：1小时
   - 对 `Bluetooth::queryRemoteDevServices` 和 `Bluetooth::emitServiceState` 函数进行了详细的代码审查，增加了详细的日志信息。

3. 问题复现与验证 [维护]
   - 工时：4小时
   - 通过不同的 `usleep` 时间进行测试，删除大日志文件后，重启设备，连接成功。

4. 代码优化与验证 [维护]
   - 工时：1小时
   - 对 `Bluetooth::emitServiceState` 函数进行代码优化，去除不必要的休眠，确保代码执行效率。

5. 文档完善 [管理]
   - 工时：2小时
   - 完善现场分析文档，将复现流程步骤写入文档中。


