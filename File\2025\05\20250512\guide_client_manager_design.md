# GuideClientManager设计文档

## 1. 概述

`GuideClientManager`是T95项目中用于管理高德红外设备的核心类，负责与高德红外设备的通信、数据处理以及设备控制。本文档详细描述了`GuideClientManager`的设计、实现方式，以及它如何与高德红外设备进行对接。

## 2. 系统架构

### 2.1 整体架构

高德红外模块的整体架构采用分层设计，自底向上包括：

1. **硬件层**：高德红外设备硬件
2. **通信层**：UDP和HTTP通信
3. **管理层**：`GuideClientManager`
4. **服务层**：`InfraredService`
5. **视图层**：`GuideInfraredView`等

### 2.2 架构图

```mermaid
graph TD
    A[高德红外设备] --- B[通信接口: UDP/HTTP]
    B --- C[GuideClientManager]
    C --- D[InfraredService]
    D --- E[GuideInfraredView等视图层]
    
    subgraph 硬件层
    A
    end
    
    subgraph 通信层
    B
    end
    
    subgraph 管理层
    C
    end
    
    subgraph 服务层
    D
    end
    
    subgraph 视图层
    E
    end
```

### 2.3 模块关系

`GuideClientManager` 作为核心管理类，与其他模块的关系如下：

```mermaid
classDiagram
    class QObject {
        <<Qt基类>>
    }
    
    class GuideClientManager {
        +instance()
        +connectInfraredDev()
        +disconnectInfraredDev()
        +getLiveTemperatureInfo()
        +getTemperatureParamInfo()
        +setTemperatureParamInfo()
        +getCalibrateParamInfo()
        +setCalibrateParamInfo()
        +ctrlLiveVideo()
        +ctrlSavePictures()
        +setPalette()
        +setTemperatureRange()
        +ctrlLaser()
        +ctrlAuxiliaryLighting()
        +ctrlLED()
        +ctrlFocus()
        +ctrlZoom()
        +y16Data2Temperature()
    }
    
    class HttpClientBean {
        +instance()
        +get()
        +post()
    }
    
    class UdpBean {
        +bindAndListen()
        +readAll()
    }
    
    class InfraredService {
        +instance()
        +initModule()
        +deinitModule()
        +startReadData()
        +stopReadData()
    }
    
    class GuideInfraredDataSave {
        +saveData()
        +getDataFromFile()
    }
    
    QObject <|-- GuideClientManager
    QObject <|-- HttpClientBean
    QObject <|-- UdpBean
    QObject <|-- InfraredService
    DataSave <|-- GuideInfraredDataSave
    
    GuideClientManager --> HttpClientBean : 使用
    GuideClientManager --> UdpBean : 使用
    InfraredService --> GuideClientManager : 使用
    GuideClientManager ..> GuideInfraredDataSave : 依赖
```

## 3. GuideClientManager类设计

### 3.1 类职责

`GuideClientManager`作为高德红外设备的管理类，主要负责：

1. 管理与高德红外设备的连接和断开
2. 通过UDP接收红外图像数据(RGB数据和Y16数据)
3. 通过HTTP进行设备控制和参数设置
4. 处理温度数据的转换和计算
5. 控制设备功能，如激光、LED、变焦等
6. 管理设备的工作模式和状态

### 3.2 类接口

`GuideClientManager`提供了丰富的接口，主要分为以下几类：

#### 3.2.1 设备连接管理

```cpp
// 获取模块单例
static GuideClientManager* instance();

// 设置本地视频流接收信息
void setLocalVideoStreamRecvInfo(const Guide::VideoStreamRecvInfo &strRecvInfo);

// 设置Http服务信息
void setHttpServerInfo(const QString &qstrAddr, quint16 qui16Port);

// 连接红外设备
bool connectInfraredDev(Guide::GuideDevInfo &stDevInfo);

// 是否已连接红外设备
bool isConnected();

// 断开连接红外设备
bool disconnectInfraredDev();
```

#### 3.2.2 参数获取与设置

```cpp
// 获取实时温度信息
bool getLiveTemperatureInfo(Guide::LiveTemperatureInfo &stLiveTempInfo);

// 获取温度参数信息
bool getTemperatureParamInfo(Guide::TemperatureParameterInfo &stTempParamInfo);

// 设置温度参数信息
bool setTemperatureParamInfo(const Guide::TemperatureParameterInfo &stTempParamInfo);

// 获取校准参数信息
bool getCalibrateParamInfo(Guide::CalibrateParameterInfo &stCalibrateParamInfo);

// 设置校准参数信息
bool setCalibrateParamInfo(const Guide::CalibrateParameterInfo &stCalibrateParamInfo);
```

#### 3.2.3 设备控制

```cpp
// 控制实时视频
bool ctrlLiveVideo(Guide::CtrlVideoVal eVal);

// 控制保存图片
bool ctrlSavePictures(Guide::SwitchValue eLedVal, Guide::SavePicturesInfo &stSavePictureInfo);

// 设置色带
bool setPalette(Guide::PaletteVal eVal);

// 设置温度范围
bool setTemperatureRange(const Guide::TemperatureRangeInfo &stTempRangeInfo);

// 控制激光
bool ctrlLaser(Guide::SwitchValue eVal);

// 控制辅助照明
bool ctrlAuxiliaryLighting(Guide::SwitchValue eVal);

// 控制LED
bool ctrlLED(Guide::SwitchValue eVal);

// 控制变焦
bool ctrlFocus(Guide::FocusValue eVal);

// 控制缩放
bool ctrlZoom(const Guide::ZoomCtrlInfo &stZommCtrlInfo);

// 控制数据切换
bool ctrlDataSwitch(Guide::CtrlDataSwitch eVal);
```

#### 3.2.4 数据处理与转换

```cpp
// Y16数据转温度数据
bool y16Data2Temperature(const QVector<short> &qvtY16Datas, QVector<double> &qvtTemperatues);

// 单个Y16数据转温度
float y16Data2Temperature(const short sY16Data, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo);

// 批量Y16数据转温度
bool y16Data2Temperature(short* pY16Data, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo, int iImageWidth, int iImageHeight, float* pTemperatureData);

// 温度转Y16数据
short temperature2Y16Data(const float fTemperature, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo);

// 批量温度转Y16数据
bool temperature2Y16Data(float* pTemperatureData, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo, int iImageWidth, int iImageHeight, short* pY16Data);
```

### 3.3 信号与槽

`GuideClientManager`通过信号与槽机制将红外数据和状态通知给上层应用：

```cpp
// 信号
signals:
    // RGB数据就绪信号
    void sigRgbData(QSharedPointer<Guide::GuideRGBDataInfo> qspGuideRgbDataInfo);
    
    // Y16数据就绪信号
    void sigY16Data(QSharedPointer<Guide::GuideY16DataInfo> qspGuideY16DataInfo);
    
    // 连接红外设备完成信号
    void sigConnectInfraredDevFinished(bool bSuccess);
    
    // 连接红外设备状态信号
    void sigConnectInfraredDevState(bool bConnectState);

// 槽
private slots:
    // RGB数据就绪读取槽
    void onRgbDataReadyRead(qint64 qi64AvailableLen);
    
    // Y16数据就绪读取槽
    void onY16DataReadyRead(qint64 qi64AvailableLen);
    
    // 连接红外设备完成槽
    void onConnectInfraredDevFinished();
```

## 4. 与高德红外设备的对接

### 4.1 通信方式

`GuideClientManager`与高德红外设备的通信采用两种方式：

1. **UDP通信**：用于接收流式数据，如RGB图像和Y16温度数据
2. **HTTP通信**：用于发送控制命令和获取设备参数

### 4.2 HTTP API端点

高德红外设备提供了一系列HTTP API端点，这些端点在`guide_httpdata_def.h`文件中定义，主要包括：

#### 4.2.1 设备连接管理

```cpp
// 连接红外设备
const char* const CONNECT_URL = "/infraredconvertdev/connect";

// 断开红外设备连接
const char* const DISCONNECT_URL = "/infraredconvertdev/disconnect";
```

#### 4.2.2 参数获取接口

```cpp
// 获取实时温度信息
const char* const GET_LIVE_TEMPERATURE_URL = "/infraredconvertdev/get/livetemperature";

// 获取温度参数信息
const char* const GET_TEMPERATURE_PARAM_URL = "/infraredconvertdev/get/temperatureparameters";

// 获取校准参数信息
const char* const GET_CALIBATE_PARAM_URL = "/infraredconvertdev/get/calibrateparameters";

// 获取设备曲线数据
const char* const GET_DEVICE_CURVE_DATA_URL = "/infraredconvertdev/get/devicecurvedata";

// Y16数据转温度数据
const char* const GET_Y16_TO_TEMPERATURE_URL = "/infraredconvertdev/get/y16totemperature";
```

#### 4.2.3 参数设置接口

```cpp
// 设置温度参数
const char* const SET_TEMPERATURE_PARAM_URL = "/infraredconvertdev/set/temperatureparameters";

// 设置校准参数
const char* const SET_CALIBATE_PARAM_URL = "/infraredconvertdev/set/calibrateparameters";

// 设置调色板
const char* const SET_PALETTE_URL = "/infraredconvertdev/set/palette";

// 设置温度范围
const char* const SET_TEMPERATURE_RANGE_URL = "/infraredconvertdev/set/temperaturerange";
```

#### 4.2.4 设备控制接口

```cpp
// 控制实时视频(冻结/恢复)
const char* const CTRL_LIVE_VIDEO_URL = "/infraredconvertdev/ctrl/livevideo";

// 控制保存图片
const char* const CTRL_SAVE_PICTURES_URL = "/infraredconvertdev/ctrl/savepictures";

// 控制激光
const char* const CTRL_LASER_URL = "/infraredconvertdev/ctrl/laser";

// 控制LED
const char* const CTRL_LED_URL = "/infraredconvertdev/ctrl/led";

// 控制变焦
const char* const CTRL_FOCUS_URL = "/infraredconvertdev/ctrl/focus";

// 控制缩放
const char* const CTRL_ZOOM_URL = "/infraredconvertdev/ctrl/zoom";

// 控制数据切换模式(红外/画中画/数码相机)
const char* const CTRL_DATASWITCH_URL = "/infraredconvertdev/ctrl/dataswitch";
```

### 4.3 通信流程

#### 4.3.1 设备连接流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant CM as GuideClientManager
    participant HTTP as HttpClientBean
    participant UDP as UdpBean
    participant Device as 高德红外设备
    
    App->>CM: connectInfraredDev()
    CM->>CM: processConnectInfraredDev()
    CM->>HTTP: 发送HTTP请求(CONNECT_URL)
    HTTP->>Device: HTTP请求: /infraredconvertdev/connect
    Device-->>HTTP: 返回连接结果
    HTTP-->>CM: 返回连接结果
    
    alt 连接成功
        CM->>UDP: openUdpComm()
        UDP->>UDP: 绑定UDP端口(RGB和Y16数据)
        CM->>CM: 启动设备状态监控定时器
        CM-->>App: sigConnectInfraredDevFinished(true)
    else 连接失败
        CM-->>App: sigConnectInfraredDevFinished(false)
    end
```

#### 4.3.2 数据接收流程

```mermaid
sequenceDiagram
    participant Device as 高德红外设备
    participant UDP as UdpBean
    participant CM as GuideClientManager
    participant App as 应用层
    
    Device->>UDP: 发送RGB数据(UDP_RGB_PORT)
    UDP->>CM: onRgbDataReadyRead()
    CM->>CM: 解析RGB数据
    CM-->>App: sigRgbData()
    
    Device->>UDP: 发送Y16数据(UDP_Y16_PORT)
    UDP->>CM: onY16DataReadyRead()
    CM->>CM: 解析Y16数据
    CM->>CM: y16Data2Temperature()
    CM-->>App: sigY16Data()
```

#### 4.3.3 设备控制流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant CM as GuideClientManager
    participant HTTP as HttpClientBean
    participant Device as 高德红外设备
    
    App->>CM: 调用控制方法(如ctrlLaser())
    CM->>CM: 构建请求数据
    CM->>HTTP: HTTP请求(如CTRL_LASER_URL)
    HTTP->>Device: 发送HTTP请求(如/infraredconvertdev/ctrl/laser)
    Device-->>HTTP: 返回执行结果
    HTTP-->>CM: 返回执行结果
    
    alt 操作成功
        CM->>CM: 更新本地配置(如激光状态)
        CM-->>App: 返回true
    else 操作失败
        CM-->>App: 返回false
    end
```

### 4.4 数据格式

高德红外设备传输的主要数据格式包括：

#### 4.4.1 RGB数据

RGB数据用于显示可见的红外图像，格式如下：

```cpp
struct GuideRGBDataInfo
{
    // RGB数据
    int iImageWidth;     // 图像宽度
    int iImageHeight;    // 图像高度
    QByteArray qbaRGBData; // RGB数据，整个qbaRGBData大小为（iImageWidth * iImageHeight * 3）
};
```

#### 4.4.2 Y16数据

Y16数据包含温度信息，用于温度分析和显示，格式如下：

```cpp
struct GuideY16DataInfo
{
    // Y16数据
    int iImageWidth;     // 图像宽度
    int iImageHeight;    // 图像高度
    LiveTemperatureInfo stLiveTempInfo; // 实时温度信息
    QByteArray qbaY16Data; // Y16数据，单个Y16数值是short类型，高德以小端存储
    QByteArray qbaMeasureParamData; // 测温参数, char型数据
    VisibleDataInfo stVisibleDataInfo; //可见光数据信息
};
```

#### 4.4.3 温度参数信息

温度参数影响测温精度，格式如下：

```cpp
struct TemperatureParameterInfo
{
    //温度参数信息
    int iBGTempSwitch;     // 背景温度开关
    double dHumidity;      // 湿度 0%-100%，即（0-100）
    double dEmissivity;    // 发射率 0.01-1.00
    double dDistance;      // 测试距离 0.4-25m
    double dBGTemperature; // 背景温度 0.1℃-100℃
};
```

### 4.5 数据转换

高德红外设备传输的Y16数据需要转换为实际温度，GuideClientManager提供了专门的转换功能：

```cpp
float GuideClientManager::y16Data2Temperature(const short sY16Data, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo)
{
    // 通过高德提供的SDK函数进行Y16到温度的转换
    float fTemperatureVal = 0.0;
    if (m_handle >= 0)
    {
        int iRet = GuideMeasureTempCallbacks::ConvertY16ToTemperature(m_handle, sY16Data, qbaMeasureParam.data(), qbaMeasureParam.size(), &fTemperatureVal);
        if (0 != iRet)
        {
            return -273.15;
        }
    }
    
    return fTemperatureVal;
}
```

## 5. 设备控制与管理

### 5.1 设备控制

GuideClientManager提供了全面的设备控制功能，每个控制功能都通过HTTP API与设备通信：

#### 5.1.1 视频流控制

```cpp
bool GuideClientManager::ctrlLiveVideo(Guide::CtrlVideoVal eVal)
{
    bool bRet = false;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::LIVE_VIDEO_CTRL, static_cast<int>(eVal));

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_LIVE_VIDEO_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
        }
    }
    return bRet;
}
```

#### 5.1.2 调色板控制

```cpp
bool GuideClientManager::setPalette(Guide::PaletteVal eVal)
{
    bool bRet = false;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::PALETTE_VALUE, static_cast<int>(eVal));

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::SET_PALETTE_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
        }
    }
    return bRet;
}
```

#### 5.1.3 激光控制

```cpp
bool GuideClientManager::ctrlLaser(Guide::SwitchValue eVal)
{
    bool bRet = false;
    QJson objReqData;
    objReqData.add(Guide::LASER_ON_CTRL, static_cast<int>(eVal));

    HTTPModule::HttpRequestInfo stReqInfo;
    stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_LASER_URL);
    stReqInfo.qbaReqData = objReqData.unformattedData();

    HTTPModule::HttpRsponseInfo stRspInfo;
    HTTPModule::HttpClientBean objHttpClient;
    if(objHttpClient.postData(stReqInfo, stRspInfo))
    {
        QJson objRspData(stRspInfo.qbaRspData);
        int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
        if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
        {
            bRet = true;

            // 写入到配置文件中
            QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
            set.beginGroup(IRConfig::Group_Param);
            set.setValue(IRConfig::Key_LaserControl, eVal);
            set.endGroup();
        }
    }
    return bRet;
}
```

#### 5.1.4 LED控制

```cpp
bool GuideClientManager::ctrlLED(Guide::SwitchValue eVal)
{
    bool bRet = false;
    QJson objReqData;
    objReqData.add(Guide::LED_ON_CTRL, static_cast<int>(eVal));

    HTTPModule::HttpRequestInfo stReqInfo;
    stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_LED_URL);
    stReqInfo.qbaReqData = objReqData.unformattedData();

    HTTPModule::HttpRsponseInfo stRspInfo;
    HTTPModule::HttpClientBean objHttpClient;
    if(objHttpClient.postData(stReqInfo, stRspInfo))
    {
        QJson objRspData(stRspInfo.qbaRspData);
        int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
        if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
        {
            bRet = true;
        }
    }
    return bRet;
}
```

### 5.2 设备状态监控

GuideClientManager实现了设备状态的自动监控机制：

```cpp
void GuideClientManager::startWatchConnectTimer()
{
    // 启动设备连接状态监控定时器
    if (-1 == m_iWatchConnectTimerId)
    {
        m_iWatchConnectTimerId = startTimer(2000);
    }
}

void GuideClientManager::timerEvent(QTimerEvent *e)
{
    // 处理定时器事件，定期检查设备连接状态
    if (m_iWatchConnectTimerId == e->timerId())
    {
        // 检测设备是否连接
        QString qstrPartUrl = "cmd/device/info";
        QString qstrRespData;
        
        bool bRet = HttpClientBean::instance()->get(getHttpUrl(qstrPartUrl), qstrRespData);
        if (!bRet)
        {
            // 连续失败处理，发送断开连接信号
            if (++m_iWatchConnectInvalidCount >= 3)
            {
                m_iWatchConnectInvalidCount = 0;
                
                if (m_bConnected)
                {
                    m_bConnected = false;
                    emit sigConnectInfraredDevState(false);
                }
            }
        }
        else
        {
            // 重置失败计数
            m_iWatchConnectInvalidCount = 0;
            
            if (!m_bConnected)
            {
                m_bConnected = true;
                emit sigConnectInfraredDevState(true);
            }
        }
    }
}
```

## 6. 高德红外SDK集成

### 6.1 SDK初始化

GuideClientManager集成了高德红外提供的测温SDK，用于进行Y16数据和温度的转换：

```cpp
void GuideClientManager::initMeasureTempSDK()
{
    // 初始化SDK
    if (m_handle < 0)
    {
        m_handle = GuideMeasureTempCallbacks::Init();
    }
}

void GuideClientManager::deinitMeasureTempSDK()
{
    // 释放SDK
    if (m_handle >= 0)
    {
        GuideMeasureTempCallbacks::Deinit(m_handle);
        m_handle = -1;
    }
}
```

### 6.2 Y16数据处理

测温SDK提供了Y16数据与温度数据相互转换的功能：

```cpp
bool GuideClientManager::y16Data2Temperature(short* pY16Data, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo, int iImageWidth, int iImageHeight, float* pTemperatureData)
{
    // 批量转换Y16数据为温度数据
    if (m_handle >= 0 && pY16Data && pTemperatureData)
    {
        int iRet = GuideMeasureTempCallbacks::ConvertY16ArrayToTemperatureArray(m_handle, pY16Data, iImageWidth * iImageHeight, 
            qbaMeasureParam.data(), qbaMeasureParam.size(), pTemperatureData);
        
        if (0 != iRet)
        {
            return false;
        }
        
        return true;
    }
    
    return false;
}
```

## 7. 总结

GuideClientManager作为T95项目与高德红外设备对接的核心模块，实现了以下功能：

1. 通过UDP和HTTP与高德红外设备进行双向通信
2. 管理设备连接状态，支持自动监控和重连
3. 提供完整的设备控制接口，如视频控制、激光控制、变焦控制等
4. 处理RGB图像数据和Y16温度数据，支持数据解析和转换
5. 集成高德红外SDK，提供准确的温度计算和分析功能
6. 维护设备参数和状态，确保设备正常工作

通过合理的架构设计和接口封装，GuideClientManager屏蔽了与高德红外设备通信的复杂细节，为应用层提供了简洁、易用的接口，使得红外成像和温度分析功能能够便捷地集成到T95项目中。 