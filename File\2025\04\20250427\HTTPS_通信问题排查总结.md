# T95手持终端HTTPS通信问题排查总结

## 问题描述

T95手持终端无法访问HTTPS接口，在进行SSL安全验证时失败。现有项目代码在其他开发环境中可以正常编译和运行，但在当前环境中编译后无法支持SSL安全通信。

## 排查过程

### 1. 版本信息对比分析

- 寻找了SVN代码仓库中的V4.1.4.5分支代码，但未找到
- 分析了MainBranch_AGY和V4.1.6.2_20210712两个分支的代码
- 对比后发现核心代码没有显著区别，无法直接确定问题原因

### 2. 主分支代码结构分析

- 重新梳理了主分支网络通信模块的代码结构
- 追踪了HTTPS请求相关的类和方法调用流程
- 发现应用层面的实现依赖底层QT库的SSL支持
- 应用代码本身并无明显问题，初步判断可能是编译环境或库文件问题

### 3. 核心问题发现

在代码中发现关键函数`compatibleHttpsTodo()`，其注释表明：

```cpp
void compatibleHttpsTodo()
{
    //适配V6.6根文件系统源生不支持openssl库的问题，这样通过覆盖原来的库文件即可替代
    //Adapting to V6.6 root filesystem's native lack of openssl library support by overwriting original library files
    QStringList qstrlstFileNames;
    qstrlstFileNames.clear();

    qstrlstFileNames.append("libQtNetwork.la");
    qstrlstFileNames.append("libQtNetwork.prl");
    qstrlstFileNames.append("libQtNetwork.so");
    qstrlstFileNames.append("libQtNetwork.so.4");
    qstrlstFileNames.append("libQtNetwork.so.4.8");
    qstrlstFileNames.append("libQtNetwork.so.4.8.7");

    QString qstrDstPath = "/opt/qt4.8.7/lib/";
    QString qstrSrcPath = "/opt/lib/qtnetwork/";

    for(int i = 0, iSize = qstrlstFileNames.size(); i < iSize; ++i)
    {
        infoLog() << "Copying file:" << qstrlstFileNames[i];
        FileOperUtil::copyFileToPath(qstrDstPath + qstrlstFileNames[i], qstrSrcPath + qstrlstFileNames[i]);
    }

    infoLog() << "Refreshing system disk...";
    FileOperUtil::refreshToSystemDisk();

    return;
}
```

从代码中可以看出，问题与libQtNetwork库有关，该库控制着QT环境中的网络功能，包括SSL支持。系统通过替换原有库文件的方式来解决SSL支持问题。

## 解决方案

### 1. 技术沟通与协作

与鲍工进行技术沟通，说明了问题情况和初步分析结果。鲍工确认了QT SDK环境问题，随后提供了正确配置的SDK环境文件。

### 2. 解决步骤

1. 获取鲍工提供的正确QT SDK环境文件
2. 安装并配置新的SDK环境
3. 使用新环境重新编译项目
4. 测试HTTPS接口访问

### 3. 关键技术点

- 问题本质：QT编译环境中缺少OpenSSL支持
- 解决方法：替换了包含正确OpenSSL支持的libQtNetwork库文件
- 库文件位置：`/opt/qt4.8.7/lib/`和`/opt/lib/qtnetwork/`
- 关键库文件：
  - libQtNetwork.la
  - libQtNetwork.prl
  - libQtNetwork.so
  - libQtNetwork.so.4
  - libQtNetwork.so.4.8
  - libQtNetwork.so.4.8.7

## 测试验证

重新编译项目后，成功实现了HTTPS接口访问功能，关键测试结果：

- 能够正常建立SSL连接
- 成功通过HTTPS协议访问远程API
- 数据传输过程中SSL加密正常工作
- 没有出现证书验证失败的错误

## 经验总结与建议

1. **问题隔离**：针对环境依赖类问题，应先与库和编译环境相关问题进行隔离
2. **代码搜索**：搜索关键词如"ssl"、"https"、"compatible"等能快速定位问题相关代码
3. **团队协作**：与相关领域专家（如鲍工）的沟通对解决编译环境问题至关重要
4. **文档记录**：详细记录解决过程和关键步骤，便于团队其他成员参考

## 后续建议

1. 在项目文档中明确记录QT编译环境配置要求
2. 创建自动化脚本验证SSL库是否正确配置
3. 考虑将`compatibleHttpsTodo()`函数集成到项目初始化流程中
4. 设计更健壮的库文件检测和兼容性处理机制 