# T95红外功能代码分析文档

## 目录结构
- [T95红外功能代码分析文档](#t95红外功能代码分析文档)
  - [目录结构](#目录结构)
  - [1. 红外相关模块概述](#1-红外相关模块概述)
    - [1.1 主要功能](#11-主要功能)
    - [1.2 技术栈](#12-技术栈)
  - [2. 核心功能模块](#2-核心功能模块)
    - [2.1 红外服务(InfraredService)](#21-红外服务infraredservice)
      - [2.1.1 功能定位](#211-功能定位)
      - [2.1.2 关键接口](#212-关键接口)
      - [2.1.3 信号定义](#213-信号定义)
      - [2.1.4 实现细节](#214-实现细节)
    - [2.2 红外数据管理](#22-红外数据管理)
      - [2.2.1 InfraredDataSave类](#221-infrareddatasave类)
        - [主要功能](#主要功能)
        - [实现细节](#实现细节)
      - [2.2.2 GuideInfraredDataSave类](#222-guideinfrareddatasave类)
        - [扩展功能](#扩展功能)
        - [调试功能](#调试功能)
    - [2.3 红外客户端管理(GuideClientManager)](#23-红外客户端管理guideclientmanager)
      - [2.3.1 功能定位](#231-功能定位)
      - [2.3.2 关键接口](#232-关键接口)
      - [2.3.3 通信实现](#233-通信实现)
      - [2.3.4 线程管理](#234-线程管理)
      - [2.3.5 温度测量与校准](#235-温度测量与校准)
    - [2.4 模块交互关系](#24-模块交互关系)
      - [2.4.1 调用关系](#241-调用关系)
      - [2.4.2 数据流向](#242-数据流向)
      - [2.4.3 线程模型](#243-线程模型)
  - [3. 视图层](#3-视图层)
    - [3.1 红外视图基类(InfraredViewBase)](#31-红外视图基类infraredviewbase)
    - [3.2 红外主视图(InfraredView)](#32-红外主视图infraredview)
    - [3.3 红外图像显示(InfraredImagingView)](#33-红外图像显示infraredimagingview)
  - [4. 数据模型](#4-数据模型)
    - [4.1 基础数据结构](#41-基础数据结构)
    - [4.2 参数配置结构](#42-参数配置结构)
    - [4.3 图像数据结构](#43-图像数据结构)
    - [4.4 枚举定义](#44-枚举定义)
  - [5. 工具类](#5-工具类)
  - [6. 配置文件](#6-配置文件)
  - [7. 依赖关系](#7-依赖关系)
  - [8. HTTP API接口](#8-http-api接口)
    - [8.1 设备控制接口](#81-设备控制接口)
    - [8.2 数据字段定义](#82-数据字段定义)
    - [8.3 通信协议](#83-通信协议)
  - [9. UML设计图](#9-uml设计图)
    - [9.1 UML图生成工具说明](#91-uml图生成工具说明)
    - [9.2 类图(Class Diagram)](#92-类图class-diagram)
    - [9.3 序列图(Sequence Diagram)](#93-序列图sequence-diagram)
    - [9.4 状态图(State Diagram)](#94-状态图state-diagram)
    - [9.5 组件图(Component Diagram)](#95-组件图component-diagram)
    - [9.6 用例图(Use Case Diagram)](#96-用例图use-case-diagram)
    - [9.7 时序图(Sequence Diagram) - 温度分析功能](#97-时序图sequence-diagram---温度分析功能)
    - [9.8 时序图(Sequence Diagram) - 图像处理功能](#98-时序图sequence-diagram---图像处理功能)
    - [9.9 UML图详细说明](#99-uml图详细说明)
      - [9.9.1 类图说明](#991-类图说明)
      - [9.9.2 序列图说明](#992-序列图说明)
      - [9.9.3 状态图说明](#993-状态图说明)
      - [9.9.4 组件图说明](#994-组件图说明)
      - [9.9.5 用例图说明](#995-用例图说明)
      - [9.9.6 时序图说明](#996-时序图说明)
  - [10. 代码设计分析](#10-代码设计分析)
    - [10.1 设计模式应用](#101-设计模式应用)
      - [10.1.1 单例模式](#1011-单例模式)
      - [10.1.2 工厂模式](#1012-工厂模式)
      - [10.1.3 观察者模式](#1013-观察者模式)
    - [10.2 错误处理机制](#102-错误处理机制)
      - [10.2.1 分层错误处理](#1021-分层错误处理)
      - [10.2.2 日志记录机制](#1022-日志记录机制)
    - [10.3 性能优化](#103-性能优化)
      - [10.3.1 内存管理](#1031-内存管理)
      - [10.3.2 多线程设计](#1032-多线程设计)
      - [10.3.3 数据缓存优化](#1033-数据缓存优化)
    - [10.4 安全性设计](#104-安全性设计)
      - [10.4.1 数据验证](#1041-数据验证)
      - [10.4.2 通信安全](#1042-通信安全)
    - [10.5 可扩展性设计](#105-可扩展性设计)
      - [10.5.1 多种红外设备支持](#1051-多种红外设备支持)
      - [10.5.2 模块化设计](#1052-模块化设计)

## 1. 红外相关模块概述

### 1.1 主要功能
- 红外检测
- 红外图像处理
- 红外数据采集
- 红外数据分析
- 红外报告生成

### 1.2 技术栈
- Qt框架
- C++语言
- 图像处理库
- 数据采集接口

## 2. 核心功能模块

### 2.1 红外服务(InfraredService)
红外服务模块是整个红外功能的核心协调中心，采用单例模式实现。

#### 2.1.1 功能定位
- 作为红外功能的入口点，集中管理红外设备初始化、数据采集和任务调度
- 提供多用户服务支持，继承自MultiUserService
- 负责红外设备类型判断和资源分配
- 协调各子模块之间的通信和数据流转

#### 2.1.2 关键接口
```cpp
// 单例获取方法
static InfraredService* instance();

// 数据采集控制
void startReadData();
void stopReadData();

// 参数读取
bool readParameters(Params *pstParams);
bool readParamFromFile(ObjParam &params);

// 网络配置
void setIPtoIR();
void restoreIPtoDev();

// 模块初始化
void initModule();
void deinitModule();
bool isInited();
bool isIniting();

// 设备类型
Infrared::InfraredType getInfraredType();
Infrared::InfraredType initInfraredType();
```

#### 2.1.3 信号定义
```cpp
// 数据传输信号
void sigData(QSharedPointer<Infrared::InfraredData> data, MultiServiceNS::USERID userId);

// 错误通知信号
void sigReadDataFail(MultiServiceNS::USERID userId);

// 初始化结果信号
void sigInfraredInitResult(bool bRet);

// 定时器控制信号
void sigStartTimer();
void sigStopTimer();

// 设备类型变更信号
void sigInfraredTypeChanged(Infrared::InfraredType eInfraredType);
```

#### 2.1.4 实现细节
- 采用定时器实现周期性数据采集
- 使用异步事务处理机制管理初始化和停止操作
- 实现连接失败的重试机制，提高系统稳定性
- 通过读写配置文件保存和恢复参数设置
- 支持多用户并发访问，资源共享

### 2.2 红外数据管理

红外数据管理包含两个主要实现：InfraredDataSave和GuideInfraredDataSave，分别用于不同类型红外设备的数据处理。

#### 2.2.1 InfraredDataSave类
负责红外数据的保存、加载和转换等基础功能。

##### 主要功能
- 数据文件保存
```cpp
QString saveData(void *pData);
QString saveData(void *pData, const QString &qstrSavePath);
```

- 数据文件读取
```cpp
INT32 getDataFromFile(const QString& strFileName, void *pData);
INT32 getTemperatureUnitFromFile(const QString& strFileName, DataFileNS::TemperatureUnit& eTemperatureUnit);
```

- 文件路径管理
```cpp
QString getDataTypeFolder();     // 获取数据类型文件夹
QString getFileNameSuffix();     // 获取文件后缀名
```

##### 实现细节
- 使用InfraredDataMap类管理红外数据格式
- 实现数据的序列化和反序列化
- 支持温度单位转换
- 数据格式检验和容错处理
- 使用工厂模式创建数据映射对象

#### 2.2.2 GuideInfraredDataSave类
专门用于高德红外设备数据的保存和加载，继承自InfraredDataSave。

##### 扩展功能
- 支持高德红外特有的数据结构
- 提供更详细的日志记录
- 添加高德设备特有参数的处理
- 增强错误检测和调试信息输出

##### 调试功能
```cpp
// 调试日志输出
debugLog() << "getDataFromFile() - Start";
debugLog() << "strFileName: " << strFileName;
debugLog() << "Get substation name: " << m_pInfraredDataInfo->strSubstationName;
```

### 2.3 红外客户端管理(GuideClientManager)

负责与高德红外设备的通信和控制，是红外设备接入的核心组件。

#### 2.3.1 功能定位
- 管理红外设备连接和断开
- 处理网络通信(UDP和HTTP)
- 实现设备参数配置
- 执行设备控制命令
- 接收和处理视频流数据

#### 2.3.2 关键接口
```cpp
// 单例获取方法
static GuideClientManager* instance();

// 网络配置
void setLocalVideoStreamRecvInfo(const Guide::VideoStreamRecvInfo &strRecvInfo);
void setHttpServerInfo(const QString &qstrAddr, quint16 qui16Port);

// 设备连接管理
bool connectInfraredDev(Guide::GuideDevInfo &stDevInfo);
bool disconnectInfraredDev();
bool isConnected();

// 参数获取/设置
bool getLiveTemperatureInfo(Guide::LiveTemperatureInfo &stLiveTempInfo);
bool getTemperatureParamInfo(Guide::TemperatureParameterInfo &stTempParamInfo);
bool setTemperatureParamInfo(const Guide::TemperatureParameterInfo &stTempParamInfo);
bool getCalibrateParamInfo(Guide::CalibrateParameterInfo &stCalibrateParamInfo);
bool setCalibrateParamInfo(const Guide::CalibrateParameterInfo &stCalibrateParamInfo);

// 设备控制
bool ctrlLiveVideo(Guide::CtrlVideoVal eVal);
bool ctrlSavePictures(Guide::SwitchValue eLedVal, Guide::SavePicturesInfo &stSavePictureInfo);
bool setPalette(Guide::PaletteVal eVal);
bool setTemperatureRange(const Guide::TemperatureRangeInfo &stTempRangeInfo);
bool ctrlLaser(Guide::SwitchValue eVal);
bool ctrlLED(Guide::SwitchValue eVal);
bool ctrlFocus(Guide::FocusValue eVal);
bool ctrlZoom(const Guide::ZoomCtrlInfo &stZommCtrlInfo);
bool ctrlDataSwitch(Guide::CtrlDataSwitch eVal);
```

#### 2.3.3 通信实现
- UDP通信
  - 使用UdpBean类处理UDP数据包
  - 接收视频流数据(RGB和Y16格式)
  - 实现数据包解析和组装

- HTTP通信
  - 使用HttpClientBean实现HTTP请求
  - 处理JSON格式的配置数据
  - 实现命令发送和响应处理

#### 2.3.4 线程管理
- 创建独立线程处理通信任务
- 使用QFutureWatcher实现异步任务管理
- 连接和断开操作的并发处理
- 任务状态监控和异常恢复

#### 2.3.5 温度测量与校准
- 集成测温SDK
- 管理温度曲线数据
- 实现测温参数配置
- 支持温度范围调整

### 2.4 模块交互关系

#### 2.4.1 调用关系
- InfraredService调用GuideClientManager实现设备操作
- InfraredService调用InfraredDataSave实现数据持久化
- 视图层通过InfraredService访问底层功能

#### 2.4.2 数据流向
- 红外设备 → GuideClientManager(UDP接收) → InfraredService(数据处理) → 视图层(显示)
- 视图层(用户操作) → InfraredService(命令转发) → GuideClientManager(HTTP发送) → 红外设备
- 数据保存流程: InfraredService(数据源) → InfraredDataSave(序列化) → 文件系统

#### 2.4.3 线程模型
- 主线程: UI交互和事件处理
- 通信线程: UDP数据接收和HTTP通信
- 处理线程: 数据处理和转换
- 定时器线程: 周期性任务执行

## 3. 视图层

### 3.1 红外视图基类(InfraredViewBase)
- 基础UI框架
- 站点信息管理
- 数据保存接口
- 消息提示功能
- 视图旋转支持

### 3.2 红外主视图(InfraredView)
- 继承自InfraredViewBase
- 实现红外检测主界面
- 提供按钮控制功能
- 管理数据采集和显示
- 处理用户交互事件

### 3.3 红外图像显示(InfraredImagingView)
- 图像显示功能
  - 实时数据显示
  - 图像回放支持
  - 颜色模式切换
  - 站点信息显示
- 温度分析功能
  - 温度数据显示
  - 温度单位转换
  - 参考温度设置
  - 温度信息更新
- 分析工具
  - 分析图形绘制
  - 中心点分析
  - 线温度分布曲线
  - 形状温度计算
  - 最高温度点定位

## 4. 数据模型

### 4.1 基础数据结构
```cpp
// 红外数据结构
struct InfraredData {
    UINT8* aucData;        // 原始数据
    FrameInfo stFrameInfo; // 帧信息
};

// 温度信息结构
struct TemperatureInfo {
    int iPointX;           // X坐标
    int iPointY;           // Y坐标
    double dTemperatureVal;// 温度值
};

// 实时温度信息
struct LiveTemperatureInfo {
    TemperatureInfo stMaxTempInfo;    // 最高温度信息
    TemperatureInfo stMinTempInfo;    // 最低温度信息
    TemperatureInfo stAvgTempInfo;    // 平均温度信息
    TemperatureInfo stCenterTempInfo; // 中心温度信息
};
```

### 4.2 参数配置结构
```cpp
// 温度参数信息
struct TemperatureParameterInfo {
    int iBGTempSwitch;    // 背景温度开关
    double dHumidity;     // 湿度 0%-100%
    double dEmissivity;   // 发射率 0.01-1.00
    double dDistance;     // 测试距离 0.4-25m
    double dBGTemperature;// 背景温度 0.1℃-100℃
};

// 校准参数信息
struct CalibrateParameterInfo {
    double dParamB;       // -100℃ - 100℃
    double dParamKF;      // 1-100
};

// 视频流接收信息
struct VideoStreamRecvInfo {
    QString qstrAddrInfo; // 地址信息
    int iPackageSize;     // 数据包大小
    int iSampleInterval;  // 采样间隔
};
```

### 4.3 图像数据结构
```cpp
// 可见光数据
struct VisibleDataInfo {
    int iImageWidth;      // 图像宽度
    int iImageHeight;     // 图像高度
    QByteArray qbaVisibleData; // YUV格式数据
};

// Y16数据信息
struct GuideY16DataInfo {
    int iImageWidth;      // 图像宽度
    int iImageHeight;     // 图像高度
    LiveTemperatureInfo stLiveTempInfo; // 实时温度信息
    QByteArray qbaY16Data;// Y16数据(小端存储)
    QByteArray qbaMeasureParamData; // 测温参数
    VisibleDataInfo stVisibleDataInfo; // 可见光数据
};
```

### 4.4 枚举定义
```cpp
// 显示模式
enum CtrlDataSwitch {
    DISPLAY_INFRARED = 0,          // 红外
    DISPLAY_PICTURE_IN_PICTURE,    // 画中画
    DISPLAY_DIGITAL_CAMERA,        // 数码相机
};

// 调色板类型
enum PaletteVal {
    PALETTE_VAL0 = 0, // 白热 WhiteHot
    PALETTE_VAL1,     // 黑热 BlackHot
    PALETTE_VAL2,     // 铁红 IronRed
    PALETTE_VAL3,     // 医疗 Medical
    PALETTE_VAL4,     // 热铁 HotIron
    PALETTE_VAL5,     // 墨褐 Sepia
    PALETTE_VAL6,     // 熔岩 Fulgurite
    PALETTE_VAL7      // 彩虹 RainBow
};

// 变焦控制
enum FocusValue {
    FOCUS_AUTO = 0,      // 自动变焦
    FOCUS_NEAR_FINE = 1, // 近焦微调
    FOCUS_FAR_FINE = 2,  // 远焦微调
};

// 缩放控制
enum ZoomValue {
    ZOOM_X1 = 0, // x1
    ZOOM_X2 = 1, // x2
    ZOOM_X4 = 2, // x4
};
```

## 5. 工具类
- 文件操作工具
- 数据转换工具
- 图像处理工具
- 参数配置工具

## 6. 配置文件
- 设备参数配置
- 环境参数配置
- 显示参数配置
- 分析参数配置

## 7. 依赖关系
- 核心服务层
  - InfraredService
  - GuideClientManager
- 数据处理层
  - InfraredDataManage
  - InfraredDataSave
- 视图展示层
  - InfraredViewBase
  - InfraredView
  - InfraredImagingView 

## 8. HTTP API接口

### 8.1 设备控制接口
```cpp
// 设备连接管理
const char* const CONNECT_URL = "/infraredconvertdev/connect";
const char* const DISCONNECT_URL = "/infraredconvertdev/disconnect";

// 温度数据获取
const char* const GET_LIVE_TEMPERATURE_URL = "/infraredconvertdev/get/livetemperature";
const char* const GET_Y16_TO_TEMPERATURE_URL = "/infraredconvertdev/get/y16totemperature";
const char* const GET_DEVICE_CURVE_DATA_URL = "/infraredconvertdev/get/devicecurvedata";

// 参数获取/设置
const char* const GET_TEMPERATURE_PARAM_URL = "/infraredconvertdev/get/temperatureparameters";
const char* const SET_TEMPERATURE_PARAM_URL = "/infraredconvertdev/set/temperatureparameters";
const char* const GET_CALIBATE_PARAM_URL = "/infraredconvertdev/get/calibrateparameters";
const char* const SET_CALIBATE_PARAM_URL = "/infraredconvertdev/set/calibrateparameters";

// 显示控制
const char* const SET_PALETTE_URL = "/infraredconvertdev/set/palette";
const char* const SET_TEMPERATURE_RANGE_URL = "/infraredconvertdev/set/temperaturerange";
const char* const CTRL_LIVE_VIDEO_URL = "/infraredconvertdev/ctrl/livevideo";
const char* const CTRL_DATASWITCH_URL = "/infraredconvertdev/ctrl/dataswitch";

// 硬件控制
const char* const CTRL_SAVE_PICTURES_URL = "/infraredconvertdev/ctrl/savepictures";
const char* const CTRL_LASER_URL = "/infraredconvertdev/ctrl/laser";
const char* const CTRL_LED_URL = "/infraredconvertdev/ctrl/led";
const char* const CTRL_FOCUS_URL = "/infraredconvertdev/ctrl/focus";
const char* const CTRL_ZOOM_URL = "/infraredconvertdev/ctrl/zoom";
```

### 8.2 数据字段定义
```cpp
// 设备信息
const char* const DEV_SERIAL_NUM = "devSerialNum"; // 设备序列号
const char* const DEV_NAME = "devName";            // 设备名称
const char* const IP = "ip";                       // ip地址信息
const char* const MAC = "mac";                     // mac地址信息
const char* const SUB_NET_MASK = "subNetMask";    // 子网掩码地址
const char* const GATEWAY = "gateway";             // 网关
const char* const DNS = "dns";                     // DNS

// 端口信息
const char* const RGB_PORT = "rgbPort";           // RGB数据端口
const char* const Y16_PORT = "y16Port";           // Y16数据端口
const char* const ARM_PORT = "armPort";           // ARM端口
const char* const FPGA_PORT = "fpgaPort";         // FPGA端口

// 版本信息
const char* const ARM_VERSION = "armVersion";      // ARM版本
const char* const SDK_VERSION = "sdkVersion";      // SDK版本
const char* const FPGA_VERSION = "fpgaVersion";    // FPGA版本

// 图像参数
const char* const IMG_WIDTH = "imgWidth";          // 图像宽度
const char* const IMG_HEIGHT = "imgHeight";        // 图像高度
const char* const VISIBLEIMG_WIDTH = "visibleimgWidth";   // 可见光图像宽度
const char* const VISIBLEIMG_HEIGHT = "visibleimgHeight"; // 可见光图像高度

// 温度信息
const char* const MAX_TEMPERATURE = "maxTemperature";      // 最大温度
const char* const MIN_TEMPERATURE = "minTemperature";      // 最小温度
const char* const AVG_TEMPERATURE = "avgTemperature";      // 平均温度
const char* const CENTER_TEMPERATURE = "centerTemperature";// 中心温度

// 参数信息
const char* const TEMPERATURE_PARAMS = "temperatureParameters"; // 温度参数
const char* const EMISSIVITY = "emissivity";                   // 发射率
const char* const DISTANCE = "distance";                       // 测试距离
const char* const HUMIDITY = "humidity";                       // 湿度
const char* const BACKGROUND_TEMPERATURE = "bgTemperature";    // 背景温度
const char* const CALIBRATE_PARAMS = "calibrateParameters";    // 校准参数
const char* const PARAM_B = "parameterB";                      // 校准参数B
const char* const PARAM_KF = "parameterKF";                   // 校准参数KF

// 控制参数
const char* const LIVE_VIDEO_CTRL = "liveVideoCtrl";          // 视频控制
const char* const LASER_ON_CTRL = "laserOn";                  // 激光控制
const char* const LED_ON_CTRL = "ledOn";                      // LED控制
const char* const FOCUS_VALUE = "focusValue";                 // 自动变焦
const char* const ZOOM_VALUE = "zoomValue";                   // 电子变焦
const char* const DARASWITCH_VALUE = "dataswitchValue";       // 数据切换

// 数据标识
const char* const INFRARED_PICTURE_DATA = "infraredPictureData"; // 红外图片
const char* const VISIBLE_PICTURE_DATA = "visiblePictureData";   // 可见光图片
const char* const Y16_DATA = "y16Data";                          // Y16数据
const char* const TEMPERATURES = "temperatures";                  // 温度数据
const char* const TEMPERATURE_CURVE = "temperatureCurve";        // 温度曲线
```

### 8.3 通信协议
```cpp
// UDP协议标识
static const QByteArray UDP_HEAD_TAG = "PDSHEAD"; // UDP头信息
static const QByteArray UDP_TAIL_TAG = "PDSTAIL"; // UDP尾信息

// HTTP响应码
enum HttpRspCode {
    HTTP_RSP_CODE_SUCCESS = 10000, // 成功
    HTTP_RSP_CODE_FAIL = 20001,    // 失败
};
```

## 9. UML设计图

### 9.1 UML图生成工具说明
本项目使用Mermaid工具生成UML图，主要特点：
- 支持多种UML图类型
- 使用简单的文本描述生成图表
- Markdown原生支持
- 便于版本控制和维护

### 9.2 类图(Class Diagram)
```mermaid
classDiagram
    InfraredService --> InfraredDataManage
    InfraredService --> GuideClientManager
    InfraredViewBase <|-- InfraredView
    InfraredViewBase <|-- InfraredImagingView
    GuideClientManager --> InfraredService
    
    class InfraredService {
        +instance() InfraredService*
        +startReadData() bool
        +stopReadData() void
        +initModule() bool
        +getInfraredType() InfraredType
    }
    
    class InfraredDataManage {
        +setData() void
        +getData() InfraredData
        +clearData() void
        +generateBMPFrame() void
        +saveJPEGImage() void
    }
    
    class GuideClientManager {
        +instance() GuideClientManager*
        +openUdpComm() void
        +closeUdpComm() void
        +initMeasureTempSDK() void
        +deinitMeasureTempSDK() void
    }
    
    class InfraredViewBase {
        #m_stSiteInfo SiteInfo
        +saveData() void
        +showMessage() void
        +rotateView() void
    }
    
    class InfraredView {
        +startDetection() void
        +stopDetection() void
        +handleUserInput() void
    }
    
    class InfraredImagingView {
        +displayImage() void
        +updateTemperature() void
        +drawAnalysis() void
    }
```

### 9.3 序列图(Sequence Diagram)
```mermaid
sequenceDiagram
    actor User
    User->>InfraredView: 启动红外检测
    activate InfraredView
    InfraredView->>InfraredService: startReadData()
    activate InfraredService
    InfraredService->>GuideClientManager: openUdpComm()
    activate GuideClientManager
    GuideClientManager-->>InfraredService: 连接成功
    InfraredService-->>InfraredView: 返回成功
    deactivate InfraredService
    
    loop 数据采集循环
        GuideClientManager->>InfraredDataManage: 接收数据
        activate InfraredDataManage
        InfraredDataManage-->>InfraredView: 更新显示
        deactivate InfraredDataManage
    end
    
    User->>InfraredView: 停止检测
    activate InfraredView
    InfraredView->>InfraredService: stopReadData()
    activate InfraredService
    InfraredService->>GuideClientManager: closeUdpComm()
    activate GuideClientManager
    GuideClientManager-->>InfraredService: 断开连接
    InfraredService-->>InfraredView: 返回成功
    deactivate InfraredService
    deactivate InfraredView
```

### 9.4 状态图(State Diagram)
```mermaid
stateDiagram-v2
    [*] --> Idle: 初始化
    Idle --> Connecting: 开始连接
    Connecting --> Connected: 连接成功
    Connecting --> Error: 连接失败
    Connected --> Collecting: 开始采集
    Collecting --> Connected: 停止采集
    Connected --> Disconnecting: 断开连接
    Disconnecting --> Idle: 断开成功
    Error --> Idle: 重试
    Error --> [*]: 退出
    
    state Connected {
        [*] --> Ready
        Ready --> Processing: 数据处理
        Processing --> Ready: 处理完成
    }
```

### 9.5 组件图(Component Diagram)
```mermaid
flowchart TD
    subgraph 红外检测系统
        Service[红外服务组件]
        Data[数据管理组件]
        Client[客户端管理组件]
        View[视图组件]
        Config[配置管理组件]
        Network[网络通信组件]
        Image[图像处理组件]
    end
    
    UI[用户界面] --> View
    View --> Service
    Service --> Data
    Service --> Client
    Client --> Network
    Data --> Image
    Config --> Service
    Config --> Client
    
    classDef note fill:#f9f,stroke:#333,stroke-width:2px
    
    Service:::note
    Data:::note
    Client:::note
```

### 9.6 用例图(Use Case Diagram)
```mermaid
flowchart TD
    User((操作员))
    Admin((系统管理员))
    
    subgraph 红外检测系统
        UC1[启动红外检测]
        UC2[停止红外检测]
        UC3[参数配置]
        UC4[数据采集]
        UC5[图像显示]
        UC6[温度分析]
        UC7[数据保存]
        UC8[系统设置]
        UC9[设备管理]
    end
    
    User --> UC1
    User --> UC2
    User --> UC3
    User --> UC4
    User --> UC5
    User --> UC6
    User --> UC7
    Admin --> UC8
    Admin --> UC9
    
    UC4 -.-> UC5
    UC4 -.-> UC6
    UC5 -.-> UC7
    UC6 -.-> UC7
```

### 9.7 时序图(Sequence Diagram) - 温度分析功能
```mermaid
sequenceDiagram
    actor User
    User->>InfraredView: 选择分析区域
    activate InfraredView
    InfraredView->>InfraredService: requestTemperatureAnalysis()
    activate InfraredService
    InfraredService->>InfraredDataManage: getCurrentFrameData()
    activate InfraredDataManage
    InfraredDataManage-->>InfraredService: return FrameData
    deactivate InfraredDataManage
    
    InfraredService->>TemperatureAnalyzer: analyzeTemperature()
    activate TemperatureAnalyzer
    TemperatureAnalyzer->>TemperatureAnalyzer: calculateMaxTemperature()
    TemperatureAnalyzer->>TemperatureAnalyzer: calculateMinTemperature()
    TemperatureAnalyzer->>TemperatureAnalyzer: calculateAvgTemperature()
    TemperatureAnalyzer-->>InfraredService: return AnalysisResult
    deactivate TemperatureAnalyzer
    
    InfraredService->>InfraredView: updateTemperatureDisplay()
    InfraredView->>InfraredView: updateUI()
    InfraredView-->>User: 显示分析结果
    deactivate InfraredView
    deactivate InfraredService
```

### 9.8 时序图(Sequence Diagram) - 图像处理功能
```mermaid
sequenceDiagram
    actor User
    User->>InfraredView: 切换调色板
    activate InfraredView
    InfraredView->>ColorPaletteManager: setPaletteType()
    activate ColorPaletteManager
    ColorPaletteManager->>ImageProcessor: applyColorMap()
    activate ImageProcessor
    ImageProcessor->>FrameBuffer: getCurrentFrame()
    activate FrameBuffer
    FrameBuffer-->>ImageProcessor: return FrameData
    deactivate FrameBuffer
    
    ImageProcessor->>ImageProcessor: processColorMapping()
    ImageProcessor-->>InfraredView: updateImage()
    InfraredView->>InfraredView: refreshDisplay()
    InfraredView-->>User: 显示新图像
    deactivate ImageProcessor
    deactivate ColorPaletteManager
    deactivate InfraredView
```

### 9.9 UML图详细说明

#### 9.9.1 类图说明
- **核心服务类(InfraredService)**
  - 采用单例模式实现
  - 负责设备初始化和数据分发
  - 提供数据采集控制接口
  
- **数据管理类(InfraredDataManage)**
  - 处理原始数据和温度数据
  - 提供数据格式转换功能
  - 管理数据缓存和清理
  
- **客户端管理类(GuideClientManager)**
  - 管理设备连接和网络通信
  - 处理UDP数据接收
  - 提供参数配置接口
  
- **视图基类(InfraredViewBase)**
  - 提供基础UI框架
  - 管理站点信息
  - 实现数据保存接口
  
- **主视图类(InfraredView)**
  - 实现红外检测主界面
  - 提供用户交互控制
  - 管理数据采集流程
  
- **图像显示类(InfraredImagingView)**
  - 处理图像显示和更新
  - 实现温度分析功能
  - 提供图像处理工具

#### 9.9.2 序列图说明
- **红外检测流程**
  - 用户启动检测
  - 服务初始化设备
  - 建立数据连接
  - 开始数据采集
  - 实时显示数据
  - 用户停止检测
  
- **温度分析流程**
  - 用户选择分析区域
  - 获取当前帧数据
  - 计算温度参数
  - 更新显示结果
  
- **图像处理流程**
  - 用户切换调色板
  - 应用颜色映射
  - 处理图像数据
  - 更新显示

#### 9.9.3 状态图说明
- **初始状态(Idle)**
  - 系统初始化完成
  - 等待用户操作
  
- **连接状态(Connecting)**
  - 尝试建立设备连接
  - 处理连接错误
  
- **已连接状态(Connected)**
  - 设备连接成功
  - 准备数据采集
  
- **采集状态(Collecting)**
  - 正在进行数据采集
  - 处理实时数据
  
- **断开状态(Disconnecting)**
  - 正在断开设备连接
  - 清理资源

#### 9.9.4 组件图说明
- **红外服务组件**
  - 设备管理
  - 数据分发
  - 状态监控
  
- **数据管理组件**
  - 原始数据处理
  - 温度数据计算
  - 数据格式转换
  
- **客户端管理组件**
  - 设备连接管理
  - 网络通信处理
  - 参数配置管理
  
- **视图组件**
  - 用户界面实现
  - 数据显示
  - 用户交互
  
- **配置管理组件**
  - 参数配置
  - 系统设置
  - 环境配置
  
- **网络通信组件**
  - UDP通信
  - HTTP通信
  - 数据协议处理
  
- **图像处理组件**
  - 图像显示
  - 颜色处理
  - 图像分析

#### 9.9.5 用例图说明
- **操作员用例**
  - 启动/停止检测
  - 参数配置
  - 数据采集
  - 图像显示
  - 温度分析
  - 数据保存
  
- **系统管理员用例**
  - 系统设置
  - 设备管理
  
- **用例关系**
  - 数据采集包含图像显示和温度分析
  - 图像显示和温度分析可以扩展为数据保存

#### 9.9.6 时序图说明
- **温度分析时序**
  - 用户选择分析区域
  - 获取当前帧数据
  - 计算温度参数
  - 更新显示结果
  
- **图像处理时序**
  - 用户切换调色板
  - 应用颜色映射
  - 处理图像数据
  - 更新显示

## 10. 代码设计分析

### 10.1 设计模式应用

#### 10.1.1 单例模式
红外功能模块广泛应用了单例模式来确保系统中仅存在一个实例，例如：
```cpp
// InfraredService的单例实现
static InfraredService* InfraredService::instance()
{
    static InfraredService objService;
    return &objService;
}

// GuideClientManager的单例实现
static GuideClientManager* GuideClientManager::instance()
{
    static GuideClientManager objManager;
    return &objManager;
}
```
单例模式确保了关键服务对象在整个应用程序中的唯一性，避免了资源冲突和状态不一致的问题。

#### 10.1.2 工厂模式
在数据处理中应用了工厂模式来创建不同类型的数据映射对象：
```cpp
// 在InfraredDataSave构造函数中注册工厂类
InfraredDataSave::InfraredDataSave()
{
    m_pInfraredDataInfo = NULL;
    MapDataFactory::registerClass<InfraredDataMap>(XML_FILE_NODE_INFRARED);
}
```
工厂模式使得数据映射对象的创建与使用解耦，增强了系统的可扩展性。

#### 10.1.3 观察者模式
通过Qt的信号槽机制实现了观察者模式，用于数据更新和事件通知：
```cpp
// 信号定义
signals:
    void sigData(QSharedPointer<Infrared::InfraredData> data, MultiServiceNS::USERID userId);
    void sigReadDataFail(MultiServiceNS::USERID userId);
    void sigInfraredInitResult(bool bRet);
    void sigInfraredTypeChanged(Infrared::InfraredType eInfraredType);
```

### 10.2 错误处理机制

#### 10.2.1 分层错误处理
红外模块实现了分层错误处理机制，不同层级有专门的错误处理策略：

1. **网络通信层错误处理**
```cpp
if(objHttpClient.postData(stReqInfo, stRspInfo))
{
    QJson objRspData(stRspInfo.qbaRspData);
    int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
    if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
    {
        // 成功处理逻辑
    }
    else
    {
        logWarning("infrared device disconnect failed.");
    }
}
else
{
    // 网络错误处理
    logWarning("send infrared device disconnect request failed.");
}
```

2. **数据处理层错误处理**
```cpp
INT32 InfraredDataSave::getDataFromFile(const QString &strFileName, void *pData)
{
    int nRes = HC_FAILURE;
    DataFile *pDataFile = new DataFile;
    bool bRes = pDataFile->open(strFileName);
    if (!bRes)
    {
        logError("Fail to open " + strFileName);
        delete pDataFile;
        return HC_FAILURE;
    }
    
    // 其他错误处理...
    
    return nRes;
}
```

3. **设备连接失败重试机制**
```cpp
enum
{
    MAX_INIT_FAIL_TIMES = 10,
    MAX_READ_PARAMS_FAIL_TIMES = 3,
    MAX_READ_DATA_FAIL_TIMES = 20,
    MAX_PING_FAIL_TIMES = 4,
    TIME_50MS = 50,
    TIME_3S = 3000
};
```

#### 10.2.2 日志记录机制
系统实现了多级日志记录，用于问题排查和分析：
```cpp
// 错误日志
logError("Fail to open " + strFileName);

// 警告日志
logWarning("raw data len is bigger than raw array buffer size.");

// 调试日志
debugLog() << "Get substation name: " << m_pInfraredDataInfo->strSubstationName;
```

### 10.3 性能优化

#### 10.3.1 内存管理
红外模块实现了高效的内存管理策略：

1. **共享指针使用**
```cpp
// 使用QSharedPointer管理资源生命周期
m_qspRgbUdpComm = QSharedPointer<UdpBean>(new UdpBean());
m_qspY16UdpComm = QSharedPointer<UdpBean>(new UdpBean());

// 信号传递中使用共享指针
void sigData(QSharedPointer<Infrared::InfraredData> data, MultiServiceNS::USERID userId);
```

2. **资源释放的安全管理**
```cpp
if(pFile)
{
    delete pFile;
    pFile = NULL;
}
```

#### 10.3.2 多线程设计
为提高系统响应性能，采用多线程处理数据：

1. **线程对象创建**
```cpp
GuideClientManager::GuideClientManager(QObject *parent)
{
    m_pThread = new QThread;
    m_pThread->start();
    // ... 其他初始化
}
```

2. **异步任务处理**
```cpp
// 使用QFutureWatcher实现异步任务监控
QFutureWatcher<bool> m_connectFutureWatcher;
QFutureWatcher<bool> m_disconnectFutureWatcher;

// 等待异步任务完成
if (m_connectFutureWatcher.isRunning())
{
    logWarning("Wait connect finished1");
    m_connectFutureWatcher.waitForFinished();
}
```

#### 10.3.3 数据缓存优化
为提高数据访问效率，实现了数据缓存机制：
```cpp
// 从配置文件读取缓存曲线数据
QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
set.beginGroup(IRConfig::Group_Param);
set.setValue(IRConfig::Key_TemperatureCurve, qbaCurveData.toBase64().data());
set.endGroup();
```

### 10.4 安全性设计

#### 10.4.1 数据验证
在处理外部数据前进行有效性验证：
```cpp
void InfraredDataSave::setMapData(InfraredDataMap *pMap)
{
    InfraredMapNS::InfraredMapData data;

    data.pucRawData = m_pInfraredDataInfo->aucInfraredData;
    data.iRawDataLen = m_pInfraredDataInfo->uiWidth * m_pInfraredDataInfo->uiHeight * m_pInfraredDataInfo->ucPixelCount;

    int iBufSize = sizeof(m_pInfraredDataInfo->aucInfraredData);
    if(data.iRawDataLen > iBufSize)
    {
        logWarning("raw data len is bigger than raw array buffer size.");
        data.iRawDataLen = iBufSize;
    }
}
```

#### 10.4.2 通信安全
实现了网络通信的安全检查机制：
```cpp
// HTTP通信结果验证
QJson objRspData(stRspInfo.qbaRspData);
int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
{
    // 成功处理逻辑
}
else
{
    // 错误处理
}
```

### 10.5 可扩展性设计

#### 10.5.1 多种红外设备支持
通过枚举类型定义不同红外设备类型，便于系统扩展：
```cpp
typedef enum _InfraredType  //红外类型
{
    INVALID = 0,
    GUIDE,
    FILIAR
}InfraredType;
```

#### 10.5.2 模块化设计
将红外功能拆分为多个独立模块，减少耦合：
- InfraredService：核心服务管理
- GuideClientManager：客户端通信管理
- InfraredDataSave：数据保存处理
- InfraredView：用户界面显示