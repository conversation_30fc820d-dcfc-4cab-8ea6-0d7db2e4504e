# Z200系统架构开发文档

## 1. 系统概述

### 1.1 系统简介

Z200是一款面向电力设备检测的工业级应用系统，集成了多种检测技术，提供全面的数据采集、处理、分析和管理功能。系统支持现场检测和远程监控，适用于电力设备状态监测、故障诊断和预测性维护等场景。

### 1.2 系统目标

- 提供多种电力设备检测手段的统一平台
- 实现设备状态的实时监控和数据分析
- 支持现场检测和远程访问能力
- 提供全面的数据管理和报告生成功能
- 支持国际化，满足全球市场需求

## 2. 系统架构

### 2.1 架构总览

Z200系统采用模块化分层架构，主要分为以下几层：

- **应用层**：系统入口和全局管理
- **核心层**：系统核心功能和数据模型
- **服务层**：提供基础服务支持
- **模块层**：实现各种功能模块
- **移动访问层**：处理移动设备访问
- **视图层**：实现用户界面
- **通用组件层**：提供UI组件复用

### 2.2 技术栈

- **开发框架**：Qt
- **编程语言**：C++
- **数据存储**：本地文件系统和结构化数据
- **UI框架**：Qt Widgets/QML
- **国际化**：Qt Translation
- **通信协议**：TCP/IP、WebSocket、MQTT

### 2.3 架构图

系统分层架构图如下所示：

```
+----------------+    +----------------+    +----------------+
|    应用层      |    |   移动访问层   |    |     视图层     |
|   (App)        |<-->| (MobileAccess) |<-->|    (View)      |
+----------------+    +----------------+    +----------------+
        ^                     ^                    ^
        |                     |                    |
        v                     v                    v
+----------------+    +----------------+    +----------------+
|    核心层      |    |    模块层      |    |   通用组件层   |
|   (Core)       |<-->|   (Module)     |<-->|   (Widget)     |
+----------------+    +----------------+    +----------------+
        ^                     ^
        |                     |
        v                     v
+----------------+    +----------------+
|    服务层      |    |   多语言支持   |
|  (Service)     |<-->|  (Language)    |
+----------------+    +----------------+
```

## 3. 模块详解

### 3.1 应用层 (App)

负责系统启动、初始化和全局管理。

**主要组件**：
- **main.cpp**：应用入口点，初始化Qt环境和系统组件
- **exceptionutil**：全局异常处理机制
- **ConfigFileInfo**：配置文件信息管理
- **customapplication**：自定义应用实现

**职责**：
- 系统启动和初始化
- 全局异常处理
- 模块加载和管理
- 配置信息加载

### 3.2 核心层 (Core)

提供系统核心功能和基础数据模型。

**主要组件**：
- **core**：核心服务接口
- **model**：数据模型定义

**职责**：
- 定义系统核心数据结构
- 提供基础服务接口
- 管理系统状态

### 3.3 服务层 (Service)

提供基础服务支持，独立于具体业务逻辑。

**主要组件**：
- **config**：配置管理服务
- **service**：通用服务接口

**职责**：
- 配置项管理
- 提供基础服务框架
- 管理系统资源

### 3.4 模块层 (Module)

实现具体业务功能的模块集合，是系统功能的主要承载者。

#### 3.4.1 检测模块

**主要组件**：
- **ae**：超声波检测
- **tev**：暂态地电压检测
- **uhf**：特高频检测
- **hfct**：高频电流检测
- **infrared**：红外检测
- **current**：电流检测

**职责**：
- 实现各种检测技术的数据采集
- 处理原始检测数据
- 提供检测算法和分析功能

#### 3.4.2 系统模块

**主要组件**：
- **systemsetting**：系统设置管理
- **functionconfigmanager**：功能配置管理
- **statemonior**：状态监控
- **diagnosismgr**：诊断管理
- **update**：系统更新管理

**职责**：
- 管理系统设置和配置
- 监控系统状态
- 提供诊断功能
- 管理系统更新

#### 3.4.3 通信模块

**主要组件**：
- **webserver**：Web服务器
- **networkcommtest**：网络通信测试
- **vpnaccessmanager**：VPN访问管理
- **comm**：通信接口

**职责**：
- 提供网络通信服务
- 管理VPN连接
- 实现设备间通信
- 提供远程访问能力

#### 3.4.4 数据处理模块

**主要组件**：
- **dataSave**：数据保存
- **dealdata**：数据处理
- **compressutil**：压缩工具
- **ffmpeg**：视频处理
- **fileoper**：文件操作

**职责**：
- 管理数据存储
- 处理和转换数据
- 提供数据压缩功能
- 处理多媒体文件

### 3.5 移动访问层 (MobileAccess)

处理移动设备访问和远程任务管理。

**主要组件**：
- **mobileaccessservice**：移动访问服务
- **customaccesstask**：接入终端访问任务
- **DigitalAccessTask**：江苏数字访问任务
- **platformcomm**：HTTP平台通信
- **appcomm**：应用通信
- **spectrum**：频谱分析
- **distributenetaccess**：分布式网络访问

**职责**：
- 管理移动设备接入
- 处理远程任务请求
- 同步数据与主系统
- 提供远程监控能力

### 3.6 视图层 (View)

实现用户界面和交互功能。视图层是系统与用户交互的直接界面，负责展示数据和接收用户输入。

#### 3.6.1 架构设计

视图层采用基于Qt Widgets的界面框架，并遵循MVC(模型-视图-控制器)设计模式：
- **模型(Model)**：来自模块层的数据模型
- **视图(View)**：界面元素和展示组件
- **控制器(Controller)**：视图与底层功能模块的交互逻辑

视图层内部采用分层组织：
- **顶层管理**：Desktop类作为主界面容器和管理器
- **功能视图**：各种功能模块对应的专用界面
- **通用界面**：系统设置、配置等通用功能界面
- **对话框**：各类交互对话框和消息窗口

#### 3.6.2 主要组件

**核心组件**：
- **Desktop**：主桌面管理器，负责整体界面布局和功能入口管理
- **View**：视图层的基础接口类，定义与模块层的交互规范
- **ColorBlt**：颜色和样式管理类，确保界面风格统一
- **Function**：功能管理接口，处理功能项的注册和调用
- **GlobalString**：全局字符串资源管理

**功能视图组件**：
- **DigitalAccessUi**：江苏数字化访问界面，提供数字化设备访问功能
- **customaccessUi**：接入终端访问界面，提供自定义接入管理
- **detectionmode**：各种检测模式的视图界面
  - infrared：红外检测界面
  - tev：暂态地电压检测界面
  - ae：声发射检测界面
  - uhf：特高频检测界面
  - hfct：高频电流检测界面
- **systemsetview**：系统设置视图，提供系统参数配置
- **functionsconfig**：功能配置视图，管理功能模块的启用与配置
- **recordplay**：记录和回放功能界面
- **update**：系统更新界面

**辅助界面组件**：
- **splash**：启动画面和过渡界面
- **window**：各类窗口组件
- **widgets**：界面定制组件

#### 3.6.3 交互模式

视图层与其他层的交互遵循以下模式：
1. **与模块层交互**：通过接口调用和事件驱动机制
   - 视图层调用模块层功能API
   - 模块层通过信号-槽机制通知视图层更新
   - 采用命令模式处理用户操作

2. **与通用组件层交互**：组合复用
   - 视图层组合使用通用组件构建复杂界面
   - 通过属性和样式表定制组件外观

3. **与移动访问层交互**：
   - 提供远程访问的视图界面
   - 同步显示远程数据和操作结果

#### 3.6.4 技术实现

视图层主要技术特点：
- 基于Qt Widgets构建的界面框架
- 使用Qt样式表(QSS)定制界面外观
- 信号-槽机制实现界面元素与功能的松耦合
- 资源文件系统管理图标和界面资源
- 分辨率自适应设计，支持不同屏幕尺寸

#### 3.6.5 扩展机制

视图层支持以下扩展方式：
- 基于插件的界面扩展，允许新功能模块动态添加对应视图
- 主题切换机制，支持界面风格定制
- 基于配置的界面元素显示控制
- 多语言界面自动适配

#### 3.6.6 性能优化

为保证界面响应性能，采用以下优化策略：
- 懒加载机制，按需创建界面组件
- 界面元素复用，减少内存占用
- 异步数据加载，避免阻塞界面线程
- 界面元素缓存机制，提高渲染性能

**职责**：
- 提供用户界面和交互体验
- 处理用户输入和交互逻辑
- 展示数据和检测结果
- 提供配置和管理界面
- 实现不同功能模块的专用视图
- 确保界面风格统一和易用性

### 3.7 通用组件层 (Widget)

提供可复用的UI组件。

**主要组件**：
- **基础UI组件**：titlebar、statusbar、pushButton等
- **特殊组件**：loadingView、playbackView、functionPanelView等

**职责**：
- 提供统一的UI组件
- 确保界面风格一致
- 提高UI代码复用率

### 3.8 多语言支持 (Language)

提供系统的国际化支持。

**主要组件**：
- **translation**：翻译模块
- 各语言翻译文件（zh_cn、en_us、ko_kr等）

**职责**：
- 管理多语言资源
- 提供翻译服务
- 支持动态切换语言

## 4. 业务流程

### 4.1 检测业务流程

```
创建检测任务 -> 配置检测参数 -> 执行检测操作 -> 
数据采集 -> 数据处理 -> 数据分析 -> 结果显示 -> 数据存储
```

### 4.2 远程访问流程

```
移动端发起请求 -> 建立安全连接 -> 身份验证 -> 
创建访问任务 -> 执行任务 -> 数据传输 -> 结果同步
```

### 4.3 系统配置流程

```
进入配置界面 -> 修改配置参数 -> 验证参数合法性 -> 
保存配置 -> 应用配置 -> 更新系统状态
```

## 5. 数据流

### 5.1 检测数据流

检测设备 -> 检测模块 -> 数据处理模块 -> 数据存储 -> 视图显示

### 5.2 配置数据流

用户界面 -> 配置服务 -> 模块配置应用 -> 功能实现

### 5.3 远程数据流

移动设备 -> 移动访问服务 -> 系统内部模块 -> 数据处理 -> 响应返回

## 6. 扩展性设计

### 6.1 模块扩展

系统采用模块化设计，通过以下机制支持扩展：
- 清晰的模块接口定义
- 基于配置的功能启用/禁用
- 插件化架构设计

### 6.2 设备支持扩展

- 硬件适配层设计
- 驱动接口标准化
- 设备描述文件机制

### 6.3 功能扩展

- 基于功能配置管理的动态功能加载
- 可配置的UI组件
- 基于权限的功能访问控制

## 7. 安全性设计

### 7.1 数据安全

- 敏感数据加密存储
- 数据完整性校验
- 数据备份和恢复机制

### 7.2 访问安全

- 用户认证和授权
- 安全通信协议（SSL/TLS）
- VPN连接支持

### 7.3 系统安全

- 异常监控和处理
- 系统日志记录
- 安全更新机制

## 8. 多语言支持

### 8.1 翻译机制

系统使用Qt的翻译机制实现国际化：
- 使用翻译文件（.ts）管理多语言资源
- 支持动态切换语言
- 使用占位符处理动态内容

### 8.2 支持的语言

系统当前支持以下语言：
- 中文简体 (zh_cn)
- 中文繁体 (zh_tw)
- 英语 (en_us)
- 韩语 (ko_kr)
- 越南语 (vi_vn)
- 西班牙语 (es_es)
- 葡萄牙语 (pt_pt)
- 德语 (de_de)
- 法语 (fr_fr)
- 俄语 (ru_ru)
- 阿拉伯语 (ar_sa)

## 9. 开发规范

### 9.1 代码规范

- 使用C++编码规范
- 采用统一的命名约定
- 模块化和组件化开发
- 文档注释要求

### 9.2 开发流程

- 基于Git的版本控制
- 代码审查流程
- 持续集成/持续部署
- 测试驱动开发

## 10. 附录

### 10.1 架构图

详见系统架构图和业务架构图。

### 10.2 目录结构

系统主要目录结构：
- `/app` - 应用程序入口
- `/core` - 核心功能
- `/service` - 服务模块
- `/module` - 功能模块
- `/view` - 视图界面
- `/widget` - UI组件
- `/mobileAccess` - 移动访问功能
- `/config` - 配置文件
- `/lib` - 第三方库
- `/sslcertfile` - SSL证书
- `/log` - 日志文件

### 10.3 参考资料

- Qt官方文档: https://doc.qt.io/
- C++标准: https://isocpp.org/
- 电力设备检测标准
- 系统设计模式参考

---

文档版本：1.0.0  
更新日期：2023-08-15  
作者：系统架构组 