# 高德红外系统架构与设计文档

## 1. 概述

高德红外系统是T95项目中负责红外图像采集、显示和处理的重要组成部分，主要实现红外图像的实时显示、回放、分析等功能。该系统采用Qt框架开发，具有良好的跨平台特性和丰富的GUI控件支持。

本文档旨在介绍高德红外系统的软件架构、类设计和主要功能流程，帮助开发人员更好地理解系统设计和实现。

### 1.1 源文件目录结构

```mermaid
graph TD
    Root[T95/Z200] --> A[module/infrared]
    Root --> V[view/guideinfrared]
    Root --> W[widget/guideinfrared]
    
    A --> B[guide/]
    A --> C[infraredservice.h]
    A --> D[infraredservice.cpp]
    A --> E[guideinfrareddatasave.h]
    A --> F[guideinfrareddatasave.cpp]
    A --> G[InfraredDataSave.h]
    A --> H[InfraredDataSave.cpp]
    A --> I[infraredconfig.h]
    
    B --> J[guideclientmanager.h]
    B --> K[guideclientmanager.cpp]
    
    V --> V1[guideinfraredview.h]
    V --> V2[guideinfraredview.cpp]
    V --> V3[guideinfraredpdaview.h]
    V --> V4[guideinfraredpdaview.cpp]
    V --> V5[guideinfraredplayback.h]
    V --> V6[guideinfraredplayback.cpp]
    V --> V7[guideinfraredplaybackview.h]
    V --> V8[guideinfraredplaybackview.cpp]
    V --> V9[guideinfraredviewbase.h]
    V --> V10[guideinfraredviewbase.cpp]
    
    W --> W1[guideinfrareddefine.h]
    W --> W2[guideinfraredimagingview.h]
    W --> W3[guideinfraredimagingview.cpp]
    W --> W4[guideinfraredimagewidget.h]
    W --> W5[guideinfraredimagewidget.cpp]
    W --> W6[guideinfrareddatamanage.h]
    W --> W7[guideinfrareddatamanage.cpp]
    
    style Root fill:#fd8,stroke:#333,stroke-width:2px
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style V fill:#bfb,stroke:#333,stroke-width:2px
    style W fill:#bbf,stroke:#333,stroke-width:2px
```

## 2. 系统整体架构

### 2.1 架构概览

高德红外系统采用分层架构设计，自底向上包括：

1. **硬件层**：高德红外设备硬件
2. **通信层**：UDP和HTTP通信
3. **管理层**：`GuideClientManager`
4. **服务层**：`InfraredService`
5. **视图层**：`GuideInfraredView`等

同时，系统在视图层采用了MVC（Model-View-Controller）架构模式，将数据、视图和控制逻辑分离：

- **模型层（Model）**：由`GuideInfraredDataManage`类实现，负责红外数据的处理、存储和转换。
- **视图层（View）**：由`GuideInfraredViewBase`、`GuideInfraredView`、`GuideInfraredImagingView`等类实现，负责界面显示和用户交互。
- **控制层（Controller）**：由各视图类中的事件处理函数和槽函数实现，负责响应用户操作并更新模型和视图。

### 2.2 架构图

```mermaid
graph TD
    A[高德红外设备] --- B[通信接口: UDP/HTTP]
    B --- C[GuideClientManager]
    C --- D[InfraredService]
    D --- E[GuideInfraredView等视图层]
    
    subgraph 硬件层
    A
    end
    
    subgraph 通信层
    B
    end
    
    subgraph 管理层
    C
    end
    
    subgraph 服务层
    D
    end
    
    subgraph 视图层
    E
    end
```

### 2.3 视图层分层架构

在视图层内部，采用自顶向下的分层设计，包括：

1. **应用层**：`GuideInfraredView`、`GuideInfraredPlaybackView`等，提供具体的功能实现
2. **基础视图层**：`GuideInfraredViewBase`，提供通用的视图功能
3. **成像视图层**：`GuideInfraredImagingView`，专注于红外图像的显示和处理
4. **数据管理层**：`GuideInfraredDataManage`，负责红外数据的处理和管理
5. **图像控件层**：`GuideInfraredImageWidget`，负责实际的图像渲染

## 3. 模块关系

### 3.1 类关系图

```mermaid
classDiagram
    class QObject {
        <<Qt基类>>
    }
    
    class QWidget {
        <<Qt基类>>
    }
    
    class GuideClientManager {
        +instance()
        +connectInfraredDev()
        +disconnectInfraredDev()
        +getLiveTemperatureInfo()
        +getTemperatureParamInfo()
        +setTemperatureParamInfo()
        +getCalibrateParamInfo()
        +setCalibrateParamInfo()
        +ctrlLiveVideo()
        +ctrlSavePictures()
        +setPalette()
        +setTemperatureRange()
        +ctrlLaser()
        +ctrlAuxiliaryLighting()
        +ctrlLED()
        +ctrlFocus()
        +ctrlZoom()
        +y16Data2Temperature()
    }
    
    class HttpClientBean {
        +instance()
        +get()
        +post()
    }
    
    class UdpBean {
        +bindAndListen()
        +readAll()
    }
    
    class InfraredService {
        +instance()
        +initModule()
        +deinitModule()
        +startReadData()
        +stopReadData()
    }
    
    class GuideInfraredViewBase {
        +createButtonBar()
        +setButtonBar()
        +onButtonPressed()
        +onButtonValueChanged()
        +showMsgBox()
        +saveData()
        +setStationInfo()
        +showStationInfo()
        +showEvent()
        +closeEvent()
    }
    
    class GuideInfraredView {
        +saveData()
        +onButtonPressed()
        +loadData()
        +deleteData()
        +onInfraredInitResult()
        +onRgbData()
        +onY16DataInfo()
        +suspend()
        +resume()
    }
    
    class GuideInfraredPlayback {
        +setPlaybackData()
        +playbackVisibleLightPhoto()
        +playbackInfraredPhoto()
        +setColorType()
        +showTitle()
        +deleteAllItem()
        +onButtonPressed()
    }
    
    class GuideInfraredPlaybackView {
        +playbackFile()
        +onPlayNextFile()
        +onPlayLastFile()
    }
    
    class GuideInfraredImagingView {
        +setDisplayMode()
        +setPlaybackData()
        +data()
        +suspend()
        +resume()
        +deleteAll()
        +shapeItemChanged()
        +createCenterPointItem()
        +setColorType()
        +showParaSettingDialog()
        +setInfraredRgbData()
        +setY16DataInfo()
        +saveInfraredPictureAsJpeg()
    }
    
    class GuideInfraredDataManage {
        +setPlaybackData()
        +isEmpty()
        +data()
        +convertData()
        +setColorType()
        +getColorType()
        +setTemperatureScale()
        +setAutoCalculate()
        +guideRawDataToTemperature()
        +temperatureFromPos()
        +updateInfraredRgbData()
    }
    
    class GuideInfraredImageWidget {
        +setImg()
        +paintEvent()
    }
    
    class GuideInfraredDataSave {
        +saveData()
        +getDataFromFile()
    }

    QObject <|-- GuideClientManager
    QObject <|-- HttpClientBean
    QObject <|-- UdpBean
    QObject <|-- InfraredService
    QWidget <|-- GuideInfraredViewBase
    GuideInfraredViewBase <|-- GuideInfraredView
    GuideInfraredViewBase <|-- GuideInfraredPlayback
    QWidget <|-- GuideInfraredImagingView
    QWidget <|-- GuideInfraredImageWidget
    PlayBackBase <|-- GuideInfraredPlaybackView
    DataSave <|-- GuideInfraredDataSave
    
    GuideClientManager --> HttpClientBean : 使用
    GuideClientManager --> UdpBean : 使用
    InfraredService --> GuideClientManager : 使用
    GuideClientManager ..> GuideInfraredDataSave : 依赖
    GuideInfraredView --> GuideClientManager : 使用
    GuideInfraredView *-- GuideInfraredImagingView
    GuideInfraredPlayback *-- GuideInfraredImagingView
    GuideInfraredImagingView *-- GuideInfraredDataManage
    GuideInfraredImagingView *-- GuideInfraredImageWidget
    GuideInfraredPlaybackView *-- GuideInfraredPlayback
```

## 4. GuideClientManager 类设计

### 4.1 类职责

`GuideClientManager`作为高德红外设备的管理类，主要负责：

1. 管理与高德红外设备的连接和断开
2. 通过UDP接收红外图像数据(RGB数据和Y16数据)
3. 通过HTTP进行设备控制和参数设置
4. 处理温度数据的转换和计算
5. 控制设备功能，如激光、LED、变焦等
6. 管理设备的工作模式和状态

### 4.2 类接口

`GuideClientManager`提供了丰富的接口，主要分为以下几类：

#### 4.2.1 设备连接管理

```cpp
// 获取模块单例
static GuideClientManager* instance();

// 设置本地视频流接收信息
void setLocalVideoStreamRecvInfo(const Guide::VideoStreamRecvInfo &strRecvInfo);

// 设置Http服务信息
void setHttpServerInfo(const QString &qstrAddr, quint16 qui16Port);

// 连接红外设备
bool connectInfraredDev(Guide::GuideDevInfo &stDevInfo);

// 是否已连接红外设备
bool isConnected();

// 断开连接红外设备
bool disconnectInfraredDev();
```

#### 4.2.2 参数获取与设置

```cpp
// 获取实时温度信息
bool getLiveTemperatureInfo(Guide::LiveTemperatureInfo &stLiveTempInfo);

// 获取温度参数信息
bool getTemperatureParamInfo(Guide::TemperatureParameterInfo &stTempParamInfo);

// 设置温度参数信息
bool setTemperatureParamInfo(const Guide::TemperatureParameterInfo &stTempParamInfo);

// 获取校准参数信息
bool getCalibrateParamInfo(Guide::CalibrateParameterInfo &stCalibrateParamInfo);

// 设置校准参数信息
bool setCalibrateParamInfo(const Guide::CalibrateParameterInfo &stCalibrateParamInfo);
```

#### 4.2.3 设备控制

```cpp
// 控制实时视频
bool ctrlLiveVideo(Guide::CtrlVideoVal eVal);

// 控制保存图片
bool ctrlSavePictures(Guide::SwitchValue eLedVal, Guide::SavePicturesInfo &stSavePictureInfo);

// 设置色带
bool setPalette(Guide::PaletteVal eVal);

// 设置温度范围
bool setTemperatureRange(const Guide::TemperatureRangeInfo &stTempRangeInfo);

// 控制激光
bool ctrlLaser(Guide::SwitchValue eVal);

// 控制辅助照明
bool ctrlAuxiliaryLighting(Guide::SwitchValue eVal);

// 控制LED
bool ctrlLED(Guide::SwitchValue eVal);

// 控制变焦
bool ctrlFocus(Guide::FocusValue eVal);

// 控制缩放
bool ctrlZoom(const Guide::ZoomCtrlInfo &stZommCtrlInfo);
```

#### 4.2.4 数据处理与转换

```cpp
// Y16数据转温度数据
bool y16Data2Temperature(const QVector<short> &qvtY16Datas, QVector<double> &qvtTemperatues);

// 单个Y16数据转温度
float y16Data2Temperature(const short sY16Data, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo);

// 批量Y16数据转温度
bool y16Data2Temperature(short* pY16Data, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo, int iImageWidth, int iImageHeight, float* pTemperatureData);
```

### 4.3 信号与槽

`GuideClientManager`通过信号与槽机制将红外数据和状态通知给上层应用：

```cpp
// 信号
signals:
    // RGB数据就绪信号
    void sigRgbData(QSharedPointer<Guide::GuideRGBDataInfo> qspGuideRgbDataInfo);
    
    // Y16数据就绪信号
    void sigY16Data(QSharedPointer<Guide::GuideY16DataInfo> qspGuideY16DataInfo);
    
    // 连接红外设备完成信号
    void sigConnectInfraredDevFinished(bool bSuccess);
    
    // 连接红外设备状态信号
    void sigConnectInfraredDevState(bool bConnectState);
```

## 5. 视图层类设计

### 5.1 视图层类图

```mermaid
classDiagram
    class QWidget {
        <<Qt基类>>
    }
    
    class GuideInfraredViewBase {
        +createButtonBar()
        +setButtonBar()
        +onButtonPressed()
        +onButtonValueChanged()
        +showMsgBox()
        +saveData()
        +setStationInfo()
        +showStationInfo()
        +showEvent()
        +closeEvent()
    }
    
    class GuideInfraredView {
        +saveData()
        +onButtonPressed()
        +loadData()
        +deleteData()
        +onInfraredInitResult()
        +onRgbData()
        +onY16DataInfo()
        +suspend()
        +resume()
    }
    
    class GuideInfraredPlayback {
        +setPlaybackData()
        +playbackVisibleLightPhoto()
        +playbackInfraredPhoto()
        +setColorType()
        +showTitle()
        +deleteAllItem()
        +onButtonPressed()
    }
    
    class GuideInfraredPlaybackView {
        +playbackFile()
        +onPlayNextFile()
        +onPlayLastFile()
    }
    
    class GuideInfraredImagingView {
        +setDisplayMode()
        +setPlaybackData()
        +data()
        +suspend()
        +resume()
        +deleteAll()
        +shapeItemChanged()
        +createCenterPointItem()
        +setColorType()
        +showParaSettingDialog()
        +setInfraredRgbData()
        +setY16DataInfo()
        +saveInfraredPictureAsJpeg()
    }
    
    class GuideInfraredDataManage {
        +setPlaybackData()
        +isEmpty()
        +data()
        +convertData()
        +setColorType()
        +getColorType()
        +setTemperatureScale()
        +setAutoCalculate()
        +guideRawDataToTemperature()
        +temperatureFromPos()
        +updateInfraredRgbData()
    }
    
    class GuideInfraredImageWidget {
        +setImg()
        +paintEvent()
    }
    
    QWidget <|-- GuideInfraredViewBase
    GuideInfraredViewBase <|-- GuideInfraredView
    GuideInfraredViewBase <|-- GuideInfraredPlayback
    QWidget <|-- GuideInfraredImagingView
    QWidget <|-- GuideInfraredImageWidget
    PlayBackBase <|-- GuideInfraredPlaybackView
    
    GuideInfraredView *-- GuideInfraredImagingView
    GuideInfraredPlayback *-- GuideInfraredImagingView
    GuideInfraredImagingView *-- GuideInfraredDataManage
    GuideInfraredImagingView *-- GuideInfraredImageWidget
    GuideInfraredPlaybackView *-- GuideInfraredPlayback
```

### 5.2 GuideInfraredViewBase

基础视图类，提供视图的通用功能：

- 按钮栏的创建和管理
- 消息框显示
- 站点信息管理
- 视图旋转处理
- 基本事件处理（显示、关闭等）

### 5.3 GuideInfraredView

红外视图的主要实现类，继承自GuideInfraredViewBase：

- 红外设备初始化和数据采集
- 处理红外数据和可见光数据
- 实现按钮的响应逻辑
- 管理数据的加载、保存和删除
- 控制激光和辅助照明

### 5.4 GuideInfraredPlayback

红外回放视图，继承自GuideInfraredViewBase：

- 播放红外图像和可见光图像
- 设置调色板类型
- 显示文件标题
- 提供上一页、下一页等导航功能

### 5.5 GuideInfraredPlaybackView

回放视图的封装，继承自PlayBackBase：

- 管理文件回放流程
- 处理文件切换事件

### 5.6 GuideInfraredImagingView

红外成像视图，负责图像显示和处理：

- 设置显示模式（红外、画中画、数码相机）
- 处理回放数据
- 管理分析图形
- 提供图像保存功能
- 控制暂停和恢复

### 5.7 GuideInfraredDataManage

红外数据管理类，负责数据处理：

- 管理Y16数据和RGB数据
- 转换原始数据和温度数据
- 处理调色板变更
- 设置温度比例
- 提供自动计算功能

### 5.8 GuideInfraredImageWidget

红外图像显示控件，负责图像渲染：

- 图像显示
- 绘制事件处理

## 6. 通信与接口设计

### 6.1 通信方式

`GuideClientManager`与高德红外设备的通信采用两种方式：

1. **UDP通信**：用于接收流式数据，如RGB图像和Y16温度数据
2. **HTTP通信**：用于发送控制命令和获取设备参数

### 6.2 HTTP API端点

高德红外设备提供了一系列HTTP API端点，主要包括：

#### 6.2.1 设备连接管理

```cpp
// 连接红外设备
const char* const CONNECT_URL = "/infraredconvertdev/connect";

// 断开红外设备连接
const char* const DISCONNECT_URL = "/infraredconvertdev/disconnect";
```

#### 6.2.2 参数获取接口

```cpp
// 获取实时温度信息
const char* const GET_LIVE_TEMPERATURE_URL = "/infraredconvertdev/get/livetemperature";

// 获取温度参数信息
const char* const GET_TEMPERATURE_PARAM_URL = "/infraredconvertdev/get/temperatureparameters";

// 获取校准参数信息
const char* const GET_CALIBATE_PARAM_URL = "/infraredconvertdev/get/calibrateparameters";

// 获取设备曲线数据
const char* const GET_DEVICE_CURVE_DATA_URL = "/infraredconvertdev/get/devicecurvedata";

// Y16数据转温度数据
const char* const GET_Y16_TO_TEMPERATURE_URL = "/infraredconvertdev/get/y16totemperature";
```

#### 6.2.3 参数设置接口

```cpp
// 设置温度参数
const char* const SET_TEMPERATURE_PARAM_URL = "/infraredconvertdev/set/temperatureparameters";

// 设置校准参数
const char* const SET_CALIBATE_PARAM_URL = "/infraredconvertdev/set/calibrateparameters";

// 设置调色板
const char* const SET_PALETTE_URL = "/infraredconvertdev/set/palette";

// 设置温度范围
const char* const SET_TEMPERATURE_RANGE_URL = "/infraredconvertdev/set/temperaturerange";
```

#### 6.2.4 设备控制接口

```cpp
// 控制实时视频(冻结/恢复)
const char* const CTRL_LIVE_VIDEO_URL = "/infraredconvertdev/ctrl/livevideo";

// 控制保存图片
const char* const CTRL_SAVE_PICTURES_URL = "/infraredconvertdev/ctrl/savepictures";

// 控制激光
const char* const CTRL_LASER_URL = "/infraredconvertdev/ctrl/laser";

// 控制LED
const char* const CTRL_LED_URL = "/infraredconvertdev/ctrl/led";

// 控制变焦
const char* const CTRL_FOCUS_URL = "/infraredconvertdev/ctrl/focus";

// 控制缩放
const char* const CTRL_ZOOM_URL = "/infraredconvertdev/ctrl/zoom";

// 控制数据切换模式(红外/画中画/数码相机)
const char* const CTRL_DATASWITCH_URL = "/infraredconvertdev/ctrl/dataswitch";
```

### 6.3 数据格式

高德红外设备传输的主要数据格式包括：

#### 6.3.1 RGB数据

RGB数据用于显示可见的红外图像，格式如下：

```cpp
struct GuideRGBDataInfo
{
    // RGB数据
    int iImageWidth;     // 图像宽度
    int iImageHeight;    // 图像高度
    QByteArray qbaRGBData; // RGB数据，整个qbaRGBData大小为（iImageWidth * iImageHeight * 3）
};
```

#### 6.3.2 Y16数据

Y16数据包含温度信息，用于温度分析和显示，格式如下：

```cpp
struct GuideY16DataInfo
{
    // Y16数据
    int iImageWidth;     // 图像宽度
    int iImageHeight;    // 图像高度
    LiveTemperatureInfo stLiveTempInfo; // 实时温度信息
    QByteArray qbaY16Data; // Y16数据，单个Y16数值是short类型，高德以小端存储
    QByteArray qbaMeasureParamData; // 测温参数, char型数据
    VisibleDataInfo stVisibleDataInfo; //可见光数据信息
};
```

## 7. 主要功能流程

### 7.1 设备连接流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant CM as GuideClientManager
    participant HTTP as HttpClientBean
    participant UDP as UdpBean
    participant Device as 高德红外设备
    
    App->>CM: connectInfraredDev()
    CM->>CM: processConnectInfraredDev()
    CM->>HTTP: 发送HTTP请求(CONNECT_URL)
    HTTP->>Device: HTTP请求: /infraredconvertdev/connect
    Device-->>HTTP: 返回连接结果
    HTTP-->>CM: 返回连接结果
    
    alt 连接成功
        CM->>UDP: openUdpComm()
        UDP->>UDP: 绑定UDP端口(RGB和Y16数据)
        CM->>CM: 启动设备状态监控定时器
        CM-->>App: sigConnectInfraredDevFinished(true)
    else 连接失败
        CM-->>App: sigConnectInfraredDevFinished(false)
    end
```

### 7.2 红外采集与显示流程

```mermaid
sequenceDiagram
    participant User
    participant GuideInfraredView
    participant GuideClientManager
    participant GuideInfraredImagingView
    participant GuideInfraredDataManage
    
    User->>GuideInfraredView: 打开视图
    GuideInfraredView->>GuideInfraredView: showEvent()
    GuideInfraredView->>GuideClientManager: connectInfraredDev()
    GuideClientManager-->>GuideInfraredView: sigConnectInfraredDevFinished()
    
    loop 数据采集循环
        GuideClientManager-->>GuideInfraredView: sigRgbData()/sigY16Data()
        GuideInfraredView->>GuideInfraredView: onRgbData()/onY16DataInfo()
        GuideInfraredView->>GuideInfraredImagingView: setInfraredRgbData()/setY16DataInfo()
        GuideInfraredImagingView->>GuideInfraredDataManage: setInfraredRgbData()/setY16DataInfo()
        GuideInfraredDataManage->>GuideInfraredDataManage: updateInfraredRgbData()
        GuideInfraredDataManage->>GuideInfraredDataManage: calcTemperatureInfo()
        GuideInfraredImagingView->>GuideInfraredImagingView: updateTemperatureInfos()
        GuideInfraredImagingView->>GuideInfraredImagingView: updateShapeTemperatureInfos()
    end
    
    User->>GuideInfraredView: 关闭视图
    GuideInfraredView->>GuideInfraredView: closeEvent()
    GuideInfraredView->>GuideClientManager: disconnectInfraredDev()
    GuideInfraredView->>GuideInfraredView: exit()
```

### 7.3 数据保存流程

```mermaid
sequenceDiagram
    participant User
    participant GuideInfraredView
    participant GuideClientManager
    participant GuideInfraredImagingView
    participant GuideInfraredDataManage
    
    User->>GuideInfraredView: 按下保存按钮
    GuideInfraredView->>GuideInfraredView: onButtonPressed(BUTTON_SAVA_DATA)
    GuideInfraredView->>GuideInfraredView: saveData()
    GuideInfraredView->>GuideInfraredImagingView: data()
    GuideInfraredImagingView->>GuideInfraredDataManage: data()
    GuideInfraredDataManage-->>GuideInfraredImagingView: 返回数据
    GuideInfraredImagingView-->>GuideInfraredView: 返回数据
    GuideInfraredView->>GuideClientManager: ctrlSavePictures()
    GuideClientManager->>GuideClientManager: 保存数据到文件
    
    opt 保存图片
        GuideInfraredView->>GuideInfraredImagingView: saveInfraredPictureAsJpeg()
        GuideInfraredImagingView-->>GuideInfraredView: 返回保存结果
    end
    
    GuideInfraredView->>GuideInfraredView: onSaveDataFinished()
    GuideInfraredView->>User: 显示保存结果
```

### 7.4 数据回放流程

```mermaid
sequenceDiagram
    participant User
    participant GuideInfraredPlaybackView
    participant GuideInfraredPlayback
    participant GuideInfraredImagingView
    
    User->>GuideInfraredPlaybackView: 打开回放视图
    GuideInfraredPlaybackView->>GuideInfraredPlaybackView: playbackFile()
    GuideInfraredPlaybackView->>GuideInfraredPlayback: setPlaybackData()
    GuideInfraredPlayback->>GuideInfraredImagingView: setPlaybackData()
    GuideInfraredImagingView->>GuideInfraredImagingView: playbackImage()
    
    alt 红外模式
        GuideInfraredImagingView->>GuideInfraredImagingView: playbackInfraredPhoto()
    else 可见光模式
        GuideInfraredImagingView->>GuideInfraredImagingView: playbackVisibleLightPhoto()
    end
    
    GuideInfraredImagingView-->>GuideInfraredPlayback: 回放完成
    GuideInfraredPlayback-->>GuideInfraredPlaybackView: 回放完成
    GuideInfraredPlaybackView-->>User: 显示回放内容
    
    User->>GuideInfraredPlaybackView: 请求下一个文件
    GuideInfraredPlaybackView->>GuideInfraredPlaybackView: onPlayNextFile()
    GuideInfraredPlaybackView->>GuideInfraredPlayback: sigNextPageClicked()
```

## 8. 关键技术点

### 8.1 温度数据处理

高德红外实现了从原始Y16数据到温度数据的转换处理：

1. 通过`GuideClientManager::y16Data2Temperature()`方法将Y16数据转换为温度数据
2. 支持自动计算温度范围和手动设置温度范围
3. 通过`GuideInfraredDataManage::temperatureFromPos()`方法获取指定位置的温度值

### 8.2 调色板管理

系统支持多种调色板类型，通过`GuideClientManager::setPalette()`方法切换：

1. 白热：温度由低到高依次显示为由黑到白
2. 黑热：温度由低到高依次显示为由白到黑
3. 铁红：温度由低到高依次显示为黑、紫、红、橙、黄、白
4. 彩虹：温度由低到高依次显示为多种颜色

### 8.3 红外分析图形

系统支持多种分析图形，用于温度分析：

1. 点温：测量指定点的温度
2. 线温：测量线上各点的温度，并可生成温度分布曲线
3. 矩形区域：测量矩形区域内的最高温度、最低温度、平均温度
4. 圆形区域：测量圆形区域内的最高温度、最低温度、平均温度

### 8.4 显示模式

系统支持多种显示模式，通过`GuideClientManager::ctrlDataSwitch()`方法切换：

1. 红外模式：只显示红外图像
2. 画中画模式：主图像为红外，内嵌可见光图像
3. 数码相机模式：只显示可见光图像

### 8.5 高德红外SDK集成

系统集成了高德红外提供的测温SDK，用于进行Y16数据和温度的转换：

```cpp
// 初始化SDK
void GuideClientManager::initMeasureTempSDK()
{
    if (m_handle < 0)
    {
        m_handle = GuideMeasureTempCallbacks::Init();
    }
}

// Y16数据批量转换
bool GuideClientManager::y16Data2Temperature(short* pY16Data, QByteArray& qbaMeasureParam, 
    const Guide::TemperatureParameterInfo& stTempParamInfo, 
    int iImageWidth, int iImageHeight, float* pTemperatureData)
{
    if (m_handle >= 0 && pY16Data && pTemperatureData)
    {
        int iRet = GuideMeasureTempCallbacks::ConvertY16ArrayToTemperatureArray(m_handle, 
            pY16Data, iImageWidth * iImageHeight, 
            qbaMeasureParam.data(), qbaMeasureParam.size(), pTemperatureData);
        
        if (0 != iRet)
        {
            return false;
        }
        
        return true;
    }
    
    return false;
}
```

## 9. 总结

高德红外系统采用分层架构设计，将系统分为硬件层、通信层、管理层、服务层和视图层，视图层内部又采用MVC架构，实现了红外图像的采集、显示、回放和分析等功能。

系统的主要特点包括：

1. 基于Qt框架的跨平台GUI实现
2. 分层设计，职责明确，耦合度低
3. 支持UDP和HTTP双通道通信
4. 完整实现红外设备的控制功能
5. 支持多种温度分析功能和显示模式
6. 支持多种调色板类型
7. 实现了数据保存和回放功能
8. 集成高德红外SDK，提供准确的温度计算

通过本文档的学习，开发人员可以快速了解高德红外系统的架构设计和实现细节，为后续的开发和维护工作提供参考。 