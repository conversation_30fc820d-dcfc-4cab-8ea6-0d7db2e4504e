# 2025年3月工作月报

**日期范围:** 2025年3月1日 - 2025年3月31日

### 项目名称
- T95手持终端蓝牙通信与功能测试
- Z200测点管理项目
- 江苏数字化接入项目

### 本月工作内容

#### 第一周（3月3日-3月7日）
1. **江苏数字化接入项目启动** [开发、文档]
   - 完成2月份工作总结与3月份计划制定
   - 编写《T95数字化接入终端下载数据保存改进方案》
   - 调试任务下载和上传接口功能
   - 与杨洪波沟通T95固件包问题
   - 组织会议讨论T95下一个固件包需求

2. **固件包开发与测试** [开发、测试]
   - 开发UHF、HFCT、TEV、AE等普测测点下编辑测点功能
   - 协助硬件组谢兴飞调试动态库编译
   - 打包主分支4.4.0固件包
   - 测试V4.4.0版本功能
   - 协助谢兴飞处理PRPS图谱分辨率相关工作

3. **文档编写与完善** [文档]
   - 完善《PDS-T95-F 手持式带电测试仪-本体固件_V4.4.0-版本说明》文档
   - 编写提测申请单
   - 完善硬件兼容性配置说明文档
   - 维护固件包版本发布信息记录表

#### 第二周（3月10日-3月14日）
1. **江苏数字化项目开发** [开发、测试]
   - 整理江苏数字化分支代码
   - 开发普测（巡检）模式功能
   - 开发任务管理相关页面
   - 测试接入终端普测模式下AE数据功能
   - 修改江苏数字化默认配置功能代码

2. **问题分析与解决** [开发、测试]
   - 调试PRPS图谱相位点数问题
   - 排查AE单位设置问题
   - 协助排查测点功能问题
   - 测试T95自动降噪功能
   - 修复数据异常问题

3. **文档与沟通** [文档、沟通]
   - 整理巡检任务流程的业务需求
   - 协调T95硬件和配件清单版本信息
   - 了解调理器相关型号
   - 完善硬件兼容性配置说明
   - 完善固件包版本发布信息记录表

#### 第三周（3月17日-3月21日）
1. **蓝牙通信模块优化** [开发、测试]
   - 编写蓝牙模块问题文档
   - 分析蓝牙连接代码
   - 设计并实施解决方案
   - 开发心跳检测机制
   - 测试T95与手机蓝牙连接稳定性

2. **功能测试与验证** [测试]
   - 完成T95新版本功能全面测试
   - 测试普测模式下各类测点功能
   - 验证固件包V4.1.7版本功能
   - 进行解决方案验证
   - 复现蓝牙连接死机问题

3. **环境搭建与问题排查** [开发、测试]
   - 搭建本地Flask架构服务器
   - 测试T95江苏接入终端任务流程
   - 尝试搭建蓝牙抓包环境
   - 分析日志文件
   - 处理数据上传问题

#### 第四周（3月24日-3月28日）
1. **蓝牙通信模块改进** [开发、维护]
   - 优化`Bluetooth::disconnectDeviceOnly()`函数
   - 研究DEVM_DisconnectRemoteDevice API功能
   - 完成主分支心跳机制功能开发
   - 测试T95与手机蓝牙连接稳定性
   - 编写《蓝牙通信模块改进》技术文档

2. **测点管理功能优化** [开发、测试]
   - 修复测点管理功能中的数据清空问题
   - 优化删除测点后的页面显示功能
   - 实现从间隔列表添加测点时的设备信息自动显示
   - 实现删除测点按钮的状态管理功能
   - 完成所有修复内容的全面测试验证

3. **固件包发布与文档维护** [文档、维护]
   - 完成V4.1.7_Release.4和V4.1.7_Release.5版本发布
   - 更新硬件兼容性配置说明文档
   - 维护固件包版本发布信息记录表
   - 编写提测申请单
   - 整理相关备查材料

4. **江苏数字化项目支持** [开发、测试]
   - 优化江苏分支接入终端功能
   - 修复江苏数字化接入终端删除测点超时问题
   - 完成数据规范代码的差异分析
   - 完成代码分支的合并和上传

#### 第五周（3月31日）
1. **蓝牙通信模块深度优化** [开发、维护]
   - 完善蓝牙底层写入函数重试机制，解决数据传输不完整问题
   - 解决蓝牙连接状态检测机制，降低心跳消息资源占用
   - 修复蓝牙连接过程中可能导致的死机问题
   - 编写蓝牙通信优化测试方案文档

2. **江苏数字化项目问题排查** [测试、沟通]
   - 排查测点删除超时问题
   - 查看VPN日志，排查4G无法连接问题
   - 与覃发齐、鲍工进行技术沟通

3. **固件包发布与文档工作** [维护、文档]
   - 打包V4.1.7_Release.6固件包
   - 编辑3月份工作总结文档
   - 编辑4月份计划文档

### 工作成果
1. 完成了江苏数字化接入项目的功能开发和测试
2. 完成了T95固件包V4.1.7和V4.4.0版本的开发和测试
3. 优化了蓝牙通信模块的稳定性，实现了心跳检测机制
4. 修复了红外图谱温宽下限字节显示问题
5. 完善了相关技术文档和测试环境
6. 完成了测点管理功能的优化和测试
7. 完成了江苏数字化项目的代码合并工作
8. 解决了蓝牙底层写入和连接状态检测机制问题

### 工时统计
- 开发工作：[75小时]
- 测试工作：[35小时]
- 文档工作：[32小时]
- 维护工作：[24小时]
- 管理工作：[10小时]
- 分析工作：[8.5小时]
- 设计工作：[4.5小时]
- 沟通工作：[5.5小时]
- 支持工作：[10.5小时]
- 环境搭建：[2小时]
- **总工时：[211小时]**

### 下月计划
1. 继续优化蓝牙通信模块的稳定性
2. 跟进固件包测试反馈情况
3. 完善测点管理功能的测试用例
4. 继续优化江苏数字化项目的功能
5. 解决江苏数字化版本与主分支数字化校验合并
   - TEV图谱字段合并
   - AE四图谱字段合并
   - PRPD和PRPS图谱字段合并

### 项目耗时分析
本月主要工作集中在以下项目：
1. 江苏数字化接入项目：约75小时
2. T95固件包开发与测试：约55小时
3. 蓝牙通信模块优化：约45小时
4. 测点管理功能优化：约20小时
5. 其他工作（文档编写、环境搭建等）：约16小时

### 风险预估
1. 蓝牙通信模块的稳定性优化可能需要多次迭代
2. 数字化校验合并可能涉及复杂的数据结构变更
3. 测试反馈可能发现新的问题需要解决
4. 硬件兼容性问题可能影响固件包的稳定性
5. 江苏数字化项目的功能优化可能需要更多时间 