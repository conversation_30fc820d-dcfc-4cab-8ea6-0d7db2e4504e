#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

def search_excel(file_path, search_term):
    """
    搜索Excel文件中包含特定内容的单元格
    
    参数:
    file_path -- Excel文件路径
    search_term -- 要搜索的内容
    
    返回:
    无，直接打印搜索结果
    """
    try:
        print(f"正在读取文件: {file_path}")
        
        # 尝试读取Excel文件中的所有工作表
        xl = pd.ExcelFile(file_path)
        sheet_names = xl.sheet_names
        
        print(f"文件包含以下工作表: {', '.join(sheet_names)}")
        print(f"开始搜索关键词: {search_term}")
        print("-" * 50)
        
        found = False
        
        # 循环处理每个工作表
        for sheet_name in sheet_names:
            print(f"在工作表 '{sheet_name}' 中搜索:")
            
            # 读取工作表
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 将所有单元格转为字符串以便搜索
            df = df.astype(str)
            
            # 搜索每一列
            for col in df.columns:
                # 查找包含搜索词的行
                matches = df[df[col].str.contains(search_term, na=False)]
                
                if not matches.empty:
                    found = True
                    print(f"  在列 '{col}' 中找到以下匹配项:")
                    
                    # 输出匹配的行
                    for idx, row in matches.iterrows():
                        print(f"    行 {idx+2}: {row[col]}")
                        
                        # 输出该行的其他列信息
                        for other_col in df.columns:
                            if other_col != col:
                                print(f"      {other_col}: {row[other_col]}")
                        print("")
            
            print("")
        
        if not found:
            print(f"未找到包含 '{search_term}' 的内容")
        
    except Exception as e:
        print(f"出错: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("用法: python excel_search.py <excel文件路径> <搜索内容>")
        sys.exit(1)
        
    file_path = sys.argv[1]
    search_term = sys.argv[2]
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 '{file_path}' 不存在")
        sys.exit(1)
        
    search_excel(file_path, search_term) 