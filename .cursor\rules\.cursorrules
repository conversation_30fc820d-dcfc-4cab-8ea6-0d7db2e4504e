# 日报系统规则指南

## [强制执行] 时间管理规则

### 1. 时间获取流程
- [必须步骤] 在任何涉及日期的操作前，必须首先执行`python config/get_shanghai_time.py`获取当前上海时间
- [必须步骤] 所有日报、周报、月报必须使用脚本获取的上海时间，禁止使用估计或之前的时间
- [必须步骤] 即使用户没有明确要求获取时间，也必须在生成任何包含日期的文档前获取准确时间

### 2. 触发时机（必须执行时间脚本的场景）
- 用户询问当前日期时
- 创建或更新日报文件时
- 创建或更新周报文件时
- 创建或更新月报文件时
- 创建或更新任何包含日期的文档时
- 用户提及"今天"、"明天"、"昨天"等时间词时

### 3. 执行示例
```
用户：写日报
系统：[立即执行] python config/get_shanghai_time.py
      [获取时间] 2025-05-13 19:48:19
      [使用日期] # 2025年5月13日工作日报
```

### 4. 常见错误（必须避免）
- 未执行时间脚本直接使用估计日期
- 使用过期时间而非当前时间
- 未根据时间脚本结果更新日期
- 日报与实际上海时间日期不符

## 一、格式规范

### 1. 标签使用规范
- 工作标签必须使用方括号[]格式，位于任务名称之后
- 允许使用的标签类型：
  - [开发]：软件开发、代码编写、功能实现等
  - [测试]：功能测试、回归测试、验证测试等
  - [维护]：系统维护、代码维护、环境配置等
  - [文档]：文档编写、文档修改、技术说明书等
  - [设计]：系统设计、方案设计、架构设计等
  - [分析]：问题分析、代码分析、日志分析等
  - [管理]：项目管理、任务管理、团队协调等
  - [调研]：技术学习、知识研究等
  - [沟通]：团队沟通、跨部门协作等
  - [支持]：技术支持、问题排查等
- 禁止使用【】或其他格式的标签，确保格式统一

### 2. 内容结构规范
- 每个任务必须包含以下三个要素：
  - **时间**：使用[小时]格式标注，精确到0.5小时
  - **分析过程**：详细描述问题分析过程、发现的具体问题点
  - **解决方案**：具体说明采取的措施和实际效果
- 使用层级分明的条目结构，保持逻辑清晰
- 技术性描述必须具体、精确，避免模糊表述

## 二、内容规范

### 1. 描述要求
- 详细记录每项任务的：
  - 起因：为什么需要执行此任务
  - 过程：如何分析和解决问题
  - 结果：最终实现了什么效果
  - 后续：下一步的计划或遗留问题
- 包含必要的技术细节：版本号、函数名、关键参数等
- 记录与团队成员的协作情况，包括讨论内容和决策过程
- 数据准确性要求：
  - 禁止使用未经验证的数据和统计结果
  - 所有性能指标、成功率、错误率等数据必须来自实际测试或日志统计
  - 如果数据尚未验证，应明确标注"待验证"或"预计"
  - 避免使用"显著"、"明显"、"大幅"等模糊的量化描述词

### 2. 禁止使用的表达方式
- 避免使用主观评价词和模糊表述：
  - 禁用："优化"、"提升"、"增强"、"改进"、"确保"、"为了"
  - 禁用："更好"、"更快"、"更强"等比较级形容词
  - 禁用："提高用户体验"等缺乏具体指标的表述
- 严禁空泛描述，如"修复了几个bug"、"改进了一些功能"

### 3. 推荐使用的表达方式
- 使用具体、客观的描述：
  - "实现了A功能"、"完成了B模块"、"修复了C问题"
  - "添加了D接口"、"重构了E组件"、"完善了F逻辑"
- 使用量化的技术指标：
  - "将连接检测时间从120秒减少到43秒"
  - "将数据传输成功率从80%提高到98%"
- 使用明确的技术术语：
  - "实现了循环写入机制和重试逻辑"
  - "添加了版本控制字段实现乐观锁"

## 三、流程规范

### 1. 文件管理
- 工作文档存储结构:
  - `File`文件夹下按年份分类(`2024/`, `2025/`等)
  - 年份文件夹下按月份分类(`01/`, `02/`等)
  - 月份文件夹下按日期分类(格式：`YYYYMMDD/`)
- 报告类文档存储结构:
  - `archived_reports`文件夹下按年份分类(`2024/`, `2025/`等)
  - 年份文件夹下按报告类型分类(`daily/`, `weekly/`, `monthly/`)
  - 各类型文件夹下存放对应的报告文件
- 将当天工作涉及的技术文档、设计文档等放入对应日期文件夹中
- 生成日报前必须读取当天日期文件夹下的文件内容
- 日报必须保存在`archived_reports/YYYY/daily/`目录下，遵循`daily_report_YYYY_MM.md`命名格式
- 严禁在根目录创建临时日报文件

### 2. 归档管理
- 每月初自动创建新的月度日报文件
- 按月归档日报内容，确保历史记录完整可查
- 按年度组织所有报告文件，保持目录结构统一
- 严格遵循统一的文件命名和路径规范
- 年度交替时创建新的年度目录结构

## 四、日报示例

### 正确的任务描述示例
```markdown
1. **蓝牙通信模块底层写入函数改进** [开发]
   - **时间:** [3.5小时]
   - **分析过程:** 通过日志分析发现当请求写入大量数据（>2000字节）时，底层函数`Bluetooth::writeData`仅写入部分数据（约1007字节）后返回，导致数据不完整。进一步检查代码发现函数未实现循环写入和错误重试机制。
   - **解决方案:** 重构了`Bluetooth::writeData`函数，添加了循环写入和最多3次重试机制。测试表明改进后在弱网环境下数据完整传输成功率从78%提高到99.5%，有效解决了数据传输不完整问题。
```

### 错误的任务描述示例
```markdown
1. **蓝牙通信模块优化** 【开发】
   - **时间:** 3小时
   - **分析过程:** 优化了蓝牙连接性能，提升了用户体验。
   - **解决方案:** 改进了连接机制，使连接更稳定，体验更好。
```

## 五、数据安全与质量规范

### 1. 工作数据准确性
- 确保工时记录真实、准确，切勿虚报或漏报
- 工作内容必须真实反映实际完成的任务
- 技术描述必须准确，避免技术性错误
- 所有引用的版本号、接口名、函数名必须校验准确

### 2. 内容质量要求
- 内容必须具体、详实，避免笼统描述
- 每项任务的技术细节必须清晰可辨
- 严禁复制粘贴前一天的内容
- 避免包含敏感信息或未经授权的内部细节

# 月度计划规则及模板

## 月度计划规范

### 1. 文件管理规范
- 月度计划统一保存在`archived_reports/YYYY/monthly/monthly_plan_YYYY_MM.md`文件中
- 每个月份的计划使用单独的文件存储，避免多月内容混在一起
- 计划按时间顺序组织，方便查阅和管理
- 遵循年份/类型/文件的三级目录结构

### 2. 内容要求
- 月度目标必须具体、可衡量且有明确期限
- 行动计划需详细说明实现目标的具体步骤
- 必须明确责任人和参与人员，确保任务有明确的负责对象
- 计划完成时间应精确到具体日期
- 月度计划内容应与日报和周报保持一致性

### 3. 更新与跟踪
- 月初(1-3日)制定当月计划
- 月中(15日左右)进行进度检查和调整
- 月末(28-30日)进行计划完成情况总结
- 状态栏及时更新为"未开始"、"进行中"、"已完成"或"延期"

## 月度计划模板

```markdown
## YYYY年MM月计划

### 基本信息
- **制定日期:** YYYY-MM-DD
- **负责人:** [姓名]

### 月度目标及行动计划

| 目标 | 行动计划 | 责任人员 | 参与人员 | 计划完成时间 | 状态 |
|------|----------|----------|----------|--------------|------|
| [目标1] | [行动1.1] | [责任人] | [参与人员] | YYYY-MM-DD | [未开始] |
| | [行动1.2] | [责任人] | [参与人员] | YYYY-MM-DD | [未开始] |
| [目标2] | [行动2.1] | [责任人] | [参与人员] | YYYY-MM-DD | [未开始] |
| | [行动2.2] | [责任人] | [参与人员] | YYYY-MM-DD | [未开始] |
| [目标3] | [行动3.1] | [责任人] | [参与人员] | YYYY-MM-DD | [未开始] |
| | [行动3.2] | [责任人] | [参与人员] | YYYY-MM-DD | [未开始] |

### 关键资源需求
- [列出本月计划所需的关键资源]

### 预期结果
- [列出本月计划预期达成的具体成果]