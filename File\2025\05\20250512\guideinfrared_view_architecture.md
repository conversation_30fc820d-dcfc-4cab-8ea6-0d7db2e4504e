# 高德红外视图层架构与设计文档

## 1. 概述

高德红外视图层是T95项目中负责显示和处理红外图像的界面模块，主要实现红外图像的实时显示、回放、分析等功能。该模块采用Qt框架开发，具有良好的跨平台特性和丰富的GUI控件支持。

本文档旨在介绍高德红外视图层的软件架构、类设计和主要功能流程，帮助开发人员更好地理解系统设计和实现。

## 2. 软件架构

### 2.1 架构概览

高德红外视图层采用MVC（Model-View-Controller）架构模式，将数据、视图和控制逻辑分离：

- **模型层（Model）**：由`GuideInfraredDataManage`类实现，负责红外数据的处理、存储和转换。
- **视图层（View）**：由`GuideInfraredViewBase`、`GuideInfraredView`、`GuideInfraredImagingView`等类实现，负责界面显示和用户交互。
- **控制层（Controller）**：由各视图类中的事件处理函数和槽函数实现，负责响应用户操作并更新模型和视图。

### 2.2 分层架构

高德红外视图层采用分层设计，自顶向下包括：

1. **应用层**：`GuideInfraredView`、`GuideInfraredPlaybackView`等，提供具体的功能实现
2. **基础视图层**：`GuideInfraredViewBase`，提供通用的视图功能
3. **成像视图层**：`GuideInfraredImagingView`，专注于红外图像的显示和处理
4. **数据管理层**：`GuideInfraredDataManage`，负责红外数据的处理和管理
5. **图像控件层**：`GuideInfraredImageWidget`，负责实际的图像渲染

## 3. 核心类设计

### 3.1 类图

```mermaid
classDiagram
    class QWidget {
        <<Qt基类>>
    }
    
    class GuideInfraredViewBase {
        +createButtonBar()
        +setButtonBar()
        +onButtonPressed()
        +onButtonValueChanged()
        +showMsgBox()
        +saveData()
        +setStationInfo()
        +showStationInfo()
        +showEvent()
        +closeEvent()
    }
    
    class GuideInfraredView {
        +saveData()
        +onButtonPressed()
        +loadData()
        +deleteData()
        +onInfraredInitResult()
        +onRgbData()
        +onY16DataInfo()
        +suspend()
        +resume()
    }
    
    class GuideInfraredPlayback {
        +setPlaybackData()
        +playbackVisibleLightPhoto()
        +playbackInfraredPhoto()
        +setColorType()
        +showTitle()
        +deleteAllItem()
        +onButtonPressed()
    }
    
    class GuideInfraredPlaybackView {
        +playbackFile()
        +onPlayNextFile()
        +onPlayLastFile()
    }
    
    class GuideInfraredImagingView {
        +setDisplayMode()
        +setPlaybackData()
        +data()
        +suspend()
        +resume()
        +deleteAll()
        +shapeItemChanged()
        +createCenterPointItem()
        +setColorType()
        +showParaSettingDialog()
        +setInfraredRgbData()
        +setY16DataInfo()
        +saveInfraredPictureAsJpeg()
    }
    
    class GuideInfraredDataManage {
        +setPlaybackData()
        +isEmpty()
        +data()
        +convertData()
        +setColorType()
        +getColorType()
        +setTemperatureScale()
        +setAutoCalculate()
        +guideRawDataToTemperature()
        +temperatureFromPos()
        +updateInfraredRgbData()
    }
    
    class GuideInfraredImageWidget {
        +setImg()
        +paintEvent()
    }

    QWidget <|-- GuideInfraredViewBase
    GuideInfraredViewBase <|-- GuideInfraredView
    GuideInfraredViewBase <|-- GuideInfraredPlayback
    QWidget <|-- GuideInfraredImagingView
    QWidget <|-- GuideInfraredImageWidget
    PlayBackBase <|-- GuideInfraredPlaybackView
    GuideInfraredView *-- GuideInfraredImagingView
    GuideInfraredPlayback *-- GuideInfraredImagingView
    GuideInfraredImagingView *-- GuideInfraredDataManage
    GuideInfraredImagingView *-- GuideInfraredImageWidget
    GuideInfraredPlaybackView *-- GuideInfraredPlayback
```

### 3.2 类职责说明

#### 3.2.1 GuideInfraredViewBase

基础视图类，提供视图的通用功能：

- 按钮栏的创建和管理
- 消息框显示
- 站点信息管理
- 视图旋转处理
- 基本事件处理（显示、关闭等）

#### 3.2.2 GuideInfraredView

红外视图的主要实现类，继承自GuideInfraredViewBase：

- 红外设备初始化和数据采集
- 处理红外数据和可见光数据
- 实现按钮的响应逻辑
- 管理数据的加载、保存和删除
- 控制激光和辅助照明

#### 3.2.3 GuideInfraredPlayback

红外回放视图，继承自GuideInfraredViewBase：

- 播放红外图像和可见光图像
- 设置调色板类型
- 显示文件标题
- 提供上一页、下一页等导航功能

#### 3.2.4 GuideInfraredPlaybackView

回放视图的封装，继承自PlayBackBase：

- 管理文件回放流程
- 处理文件切换事件

#### 3.2.5 GuideInfraredImagingView

红外成像视图，负责图像显示和处理：

- 设置显示模式（红外、画中画、数码相机）
- 处理回放数据
- 管理分析图形
- 提供图像保存功能
- 控制暂停和恢复

#### 3.2.6 GuideInfraredDataManage

红外数据管理类，负责数据处理：

- 管理Y16数据和RGB数据
- 转换原始数据和温度数据
- 处理调色板变更
- 设置温度比例
- 提供自动计算功能

#### 3.2.7 GuideInfraredImageWidget

红外图像显示控件，负责图像渲染：

- 图像显示
- 绘制事件处理

## 4. 主要功能流程

### 4.1 红外采集与显示流程

```mermaid
sequenceDiagram
    participant User
    participant GuideInfraredView
    participant GuideInfraredImagingView
    participant GuideInfraredDataManage
    
    User->>GuideInfraredView: 打开视图
    GuideInfraredView->>GuideInfraredView: showEvent()
    GuideInfraredView->>GuideInfraredView: 初始化红外设备
    
    loop 数据采集循环
        GuideInfraredView->>GuideInfraredView: onRgbData()/onY16DataInfo()
        GuideInfraredView->>GuideInfraredImagingView: setInfraredRgbData()/setY16DataInfo()
        GuideInfraredImagingView->>GuideInfraredDataManage: setInfraredRgbData()/setY16DataInfo()
        GuideInfraredDataManage->>GuideInfraredDataManage: updateInfraredRgbData()
        GuideInfraredDataManage->>GuideInfraredDataManage: calcTemperatureInfo()
        GuideInfraredImagingView->>GuideInfraredImagingView: updateTemperatureInfos()
        GuideInfraredImagingView->>GuideInfraredImagingView: updateShapeTemperatureInfos()
    end
    
    User->>GuideInfraredView: 关闭视图
    GuideInfraredView->>GuideInfraredView: closeEvent()
    GuideInfraredView->>GuideInfraredView: exit()
```

### 4.2 数据保存流程

```mermaid
sequenceDiagram
    participant User
    participant GuideInfraredView
    participant GuideInfraredImagingView
    participant GuideInfraredDataManage
    
    User->>GuideInfraredView: 按下保存按钮
    GuideInfraredView->>GuideInfraredView: onButtonPressed(BUTTON_SAVA_DATA)
    GuideInfraredView->>GuideInfraredView: saveData()
    GuideInfraredView->>GuideInfraredImagingView: data()
    GuideInfraredImagingView->>GuideInfraredDataManage: data()
    GuideInfraredDataManage-->>GuideInfraredImagingView: 返回数据
    GuideInfraredImagingView-->>GuideInfraredView: 返回数据
    GuideInfraredView->>GuideInfraredView: 保存数据到文件
    
    opt 保存图片
        GuideInfraredView->>GuideInfraredImagingView: saveInfraredPictureAsJpeg()
        GuideInfraredImagingView-->>GuideInfraredView: 返回保存结果
    end
    
    GuideInfraredView->>GuideInfraredView: onSaveDataFinished()
    GuideInfraredView->>User: 显示保存结果
```

### 4.3 数据回放流程

```mermaid
sequenceDiagram
    participant User
    participant GuideInfraredPlaybackView
    participant GuideInfraredPlayback
    participant GuideInfraredImagingView
    
    User->>GuideInfraredPlaybackView: 打开回放视图
    GuideInfraredPlaybackView->>GuideInfraredPlaybackView: playbackFile()
    GuideInfraredPlaybackView->>GuideInfraredPlayback: setPlaybackData()
    GuideInfraredPlayback->>GuideInfraredImagingView: setPlaybackData()
    GuideInfraredImagingView->>GuideInfraredImagingView: playbackImage()
    
    alt 红外模式
        GuideInfraredImagingView->>GuideInfraredImagingView: playbackInfraredPhoto()
    else 可见光模式
        GuideInfraredImagingView->>GuideInfraredImagingView: playbackVisibleLightPhoto()
    end
    
    GuideInfraredImagingView-->>GuideInfraredPlayback: 回放完成
    GuideInfraredPlayback-->>GuideInfraredPlaybackView: 回放完成
    GuideInfraredPlaybackView-->>User: 显示回放内容
    
    User->>GuideInfraredPlaybackView: 请求下一个文件
    GuideInfraredPlaybackView->>GuideInfraredPlaybackView: onPlayNextFile()
    GuideInfraredPlaybackView->>GuideInfraredPlayback: sigNextPageClicked()
```

## 5. 关键技术点

### 5.1 温度数据处理

高德红外视图层实现了从原始Y16数据到温度数据的转换处理：

1. 通过`guideRawDataToTemperature()`方法将Y16数据转换为温度数据
2. 支持自动计算温度范围和手动设置温度范围
3. 通过`temperatureFromPos()`方法获取指定位置的温度值

### 5.2 调色板管理

系统支持多种调色板类型，通过`setColorType()`方法切换：

1. 白热：温度由低到高依次显示为由黑到白
2. 黑热：温度由低到高依次显示为由白到黑
3. 铁红：温度由低到高依次显示为黑、紫、红、橙、黄、白
4. 彩虹：温度由低到高依次显示为多种颜色

### 5.3 红外分析图形

系统支持多种分析图形，用于温度分析：

1. 点温：测量指定点的温度
2. 线温：测量线上各点的温度，并可生成温度分布曲线
3. 矩形区域：测量矩形区域内的最高温度、最低温度、平均温度
4. 圆形区域：测量圆形区域内的最高温度、最低温度、平均温度

### 5.4 显示模式

系统支持多种显示模式：

1. 红外模式：只显示红外图像
2. 画中画模式：主图像为红外，内嵌可见光图像
3. 数码相机模式：只显示可见光图像

## 6. 总结

高德红外视图层采用MVC架构模式，具有良好的分层设计和模块化结构。通过合理的类设计和功能划分，实现了红外图像的采集、显示、回放和分析等功能。系统架构具有良好的可扩展性和可维护性，能够满足红外测温系统的各项需求。

主要特点包括：

1. 基于Qt框架的跨平台GUI实现
2. 分层设计，职责明确
3. 支持多种温度分析功能
4. 支持多种显示模式和调色板类型
5. 实现了数据保存和回放功能

通过本文档的学习，开发人员可以快速了解高德红外视图层的架构设计和实现细节，为后续的开发和维护工作提供参考。 