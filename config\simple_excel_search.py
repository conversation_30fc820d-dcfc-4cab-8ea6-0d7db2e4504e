import openpyxl
import sys
import os

def search_excel(file_path, search_term):
    """
    搜索Excel文件中包含特定内容的单元格
    
    参数:
    file_path -- Excel文件路径
    search_term -- 要搜索的内容
    
    返回:
    无，直接打印搜索结果
    """
    try:
        print(f"正在读取文件: {file_path}")
        
        # 打开Excel文件
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        
        sheet_names = workbook.sheetnames
        
        print(f"文件包含以下工作表: {', '.join(sheet_names)}")
        print(f"开始搜索关键词: {search_term}")
        print("-" * 50)
        
        found = False
        
        # 循环处理每个工作表
        for sheet_name in sheet_names:
            print(f"在工作表 '{sheet_name}' 中搜索:")
            
            # 获取当前工作表
            sheet = workbook[sheet_name]
            
            # 遍历所有单元格
            for row_idx, row in enumerate(sheet.rows, 1):
                for cell in row:
                    cell_value = str(cell.value) if cell.value is not None else ""
                    
                    # 检查单元格值是否包含搜索词
                    if search_term in cell_value:
                        found = True
                        print(f"  在单元格 {cell.coordinate} 中找到匹配项: {cell_value}")
                        
                        # 打印该行的其他内容
                        print(f"  该行的完整内容:")
                        for col_idx, col_cell in enumerate(row, 1):
                            col_value = str(col_cell.value) if col_cell.value is not None else ""
                            print(f"    列 {col_idx}: {col_value}")
                        print("")
            
            print("")
        
        if not found:
            print(f"未找到包含 '{search_term}' 的内容")
        
    except Exception as e:
        print(f"出错: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("用法: python simple_excel_search.py <excel文件路径> <搜索内容>")
        sys.exit(1)
        
    file_path = sys.argv[1]
    search_term = sys.argv[2]
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 '{file_path}' 不存在")
        sys.exit(1)
        
    search_excel(file_path, search_term) 