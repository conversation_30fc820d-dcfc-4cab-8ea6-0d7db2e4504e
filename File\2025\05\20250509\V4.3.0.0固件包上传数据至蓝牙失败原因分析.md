# V4.3.0.0固件包上传数据至蓝牙失败原因分析

## 问题概述

通过蓝牙上传V4.3.0.0固件包任务数据时，出现偶发性失败，主要表现为：

*   T95手持终端提示"上传失败"
    
*   日志显示"未收到应答数据"
    
*   数据传输过程中断
    
*   这个是安卓反馈的信息数据：  
    ![1746707166592_8e788604db6b69d1f6113711be1411e0.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eYVOL5vkz1j8wlpz/img/c08c3e24-f908-4796-8418-2aa511e47a0b.png)
    

## 测试环境

*   **测试设备**：T95设备
    
*   **测试工具**：NewBluetoothServerTest-1.0.3.apk（安卓蓝牙数据校验App）
    
*   **任务数据规模**：151个间隔设备，每个设备20个测点，共3020个测点
    
*   **数据总量**：2,614,795字节（约2.5MB）
    

## 测试结果

连续上传十次，成功9次，失败1次（第九次）。失败原因为数据写入缓冲区不完整，导致应答数据丢失。

### 测试数据记录

第一次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/cb389f6d-19d8-4606-990d-e0f03fdf8aab.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/c0ea9d19-c480-4b21-8075-cc41199b4b13.jpeg)

第二次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/a3e52158-ceb9-4909-b1b1-9b431ef88ddd.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/b6eba22d-8dfa-497a-84a1-50842b3a79a1.jpeg)

第三次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/7ff466ac-804e-444a-8042-156c48237429.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/48a7cf16-c0b9-4c43-acaf-91956cb05e3a.jpeg)

第四次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/a6eeca3e-2a4d-4413-879a-98125b5560b5.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/4ca56b65-2f0e-4d43-a90c-d9c7d8e7bcc0.jpeg)

第五次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/b13e59d0-7b82-497a-8349-5f807e1647ea.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/b642a97b-7529-4755-8a7c-4d4429d27d12.jpeg)

第六次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/40a6d359-1085-495c-81cb-da2db59847d6.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/9fad4856-d3ed-452f-a599-5511b4c21c4b.jpeg)

第七次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/86a31728-87fc-46e7-b30d-df9b47635889.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/03d6fdec-f923-4032-813e-6eb69b9ea699.jpeg)

第八次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/f138b577-9688-4137-bfb0-f5003ba75ce5.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/d85e914f-c3c3-4103-b3ef-1e8cbb1337af.jpeg)

第九次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/cc58783e-0e4f-4675-8016-af601dd3fd3b.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/771b36c0-5b9c-4ea6-a36b-ce9c2872e372.jpeg)

第十次：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/74f42b9c-88a3-4a46-ada9-6606bf98ba5d.png)![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/f3d00312-7f26-4c6b-91f9-3d7da40ba073.jpeg)

## 数据传输性能分析

|  测试类型  |  数据量  |  上传时间(秒)  |  传输速率(KB/s)  |  总耗时(秒)  |
| --- | --- | --- | --- | --- |
|  UHF  |  2,614,795 Bytes  |  24.45  |  104.44  |  42.91  |
|  UHF  |  2,614,795 Bytes  |  23.21  |  110.02  |  43.90  |
|  UHF  |  2,614,795 Bytes  |  22.24  |  114.82  |  43.16  |

**平均传输速率**：109.76 KB/s

## 失败原因分析

经过分析，导致蓝牙传输失败的主要原因有：

1.  **蓝牙通信连接不稳定**：
    
    *   在数据上送过程中，蓝牙socket连接可能意外断开
        
    *   连接断开后无恢复机制，直接导致传输失败
        
2.  **缓冲区溢出问题**：
    
    *   T95当前采用4000字节/帧的分片处理方式
        
    *   每发送一帧后延时30ms
        

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/5f67f614-2ee9-46ab-af8a-b921517f4b23.png)

*   当发送速率超过蓝牙实际传输能力时，缓冲区会逐渐填满
    
*   缓冲区满后继续写入会导致数据丢失
    
*   **数据丢失现象**：
    
*   第九次测试失败时，有2020字节数据未成功写入（仅写入1980字节）![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/Lk3lbmN179176Om9/img/9b66b388-c543-4a14-a2f2-6063adbff2e2.png)
    
*   丢失数据导致接收端无法获取完整信息，因此无法发送应答
    

## 软件缺陷

目前软件架构存在以下局限：

*   只能控制写入缓冲区的数据速度，无法感知或控制缓冲区实际发送速度
    
*   缺乏传输错误的检测和恢复机制（和鲍工沟通的）
    

## 蓝牙速率（性能）测试记录（前同事留下的，鲍工提供的文档）

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/eYVOL5vkz1j8wlpz/img/637e426e-d19e-4d50-a1ea-0e04d033ecf3.png)

## 改进建议

1.  **优化延时策略**：
    
    *   基于当前平均传输速率(109.76 KB/s)计算更合理的延时参数
        
    *   考虑实现动态延时机制，根据传输状态自适应调整
        
2.  **改进数据重发机制**：
    
    *   优化Bluetooth::writeData函数的重试策略
        
    *   当SPPM\_WriteData返回0字节时增加等待时间（当前固定为10ms）
        
    *   对大数据包设置更合理的超时时间，避免传输中断
        
    *   记录累计发送的字节数，即使部分失败也能返回实际发送量
        
    *   添加详细的状态日志，便于问题定位和性能分析
        
    *   保持实现分段传输，修改调整每发送一定量数据后添加的延时时间，避免缓冲区溢出。
        

## 结论

固件包上传蓝牙传输失败问题主要源于缓冲区管理和延时策略不完善。通过优化发送延时参数、实现可靠的数据传输协议以及增强错误恢复机制，可以显著提高传输成功率。

根据测试数据，可以测试将延时从30ms进行调整，以更好地适应不同文件的平均传输速率，降低缓冲区溢出风险。

此外，通过实现数据重发机制，包括动态调整发送块大小、基于实际传输速率计算延时以及采用指数退避重试策略，可以进一步增强蓝牙传输的可靠性。