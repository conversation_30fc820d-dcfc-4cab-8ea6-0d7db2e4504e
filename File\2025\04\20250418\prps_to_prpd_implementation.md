# PRPS转PRPD实现技术文档

## 1. 概述

### 1.1 文档目的

本文档详细描述了超高频局部放电检测中PRPS(Phase Resolved Pulse Sequence, 相位分辨脉冲序列)数据转换为PRPD(Phase Resolved Pulse Density, 相位分辨脉冲密度)数据的实现原理和技术细节。

### 1.2 基本概念

- **PRPS**: 记录多个电源周期内每个相位点上放电脉冲幅值的三维数据
- **PRPD**: 统计每个相位点上不同幅值区间放电脉冲出现频次的二维数据

### 1.3 数据转换的意义

PRPS→PRPD转换是将时序采样数据转化为统计特征数据的过程，这种转换有助于:
- 压缩数据量
- 突出放电模式特征
- 便于匹配放电类型特征图谱
- 利于专家或智能算法进行故障诊断

## 2. 数据结构定义

### 2.1 PRPS数据结构

PRPS数据在逻辑上为三维结构，物理存储上通常使用二维数组实现:

```cpp
// PRPS数据定义
QVector<double> vecPrpsData;   // 实际存储结构
// 尺寸 = PHASE_INTERVAL_CNT * PERIOD_CNT (通常为60*50=3000)

// 数据访问方式
double getPrpsValue(int periodIndex, int phaseIndex) {
    return vecPrpsData[periodIndex * PHASE_INTERVAL_CNT + phaseIndex];
}
```

PRPS数据结构示意图:
```
PRPS[周期][相位] = 幅值

其中:
- 周期: 0 ~ (PERIOD_CNT-1), 通常为50个周期
- 相位: 0 ~ (PHASE_INTERVAL_CNT-1), 通常为60个相位点(对应0°-360°)
- 幅值: 放电信号强度, 通常以dB或dBm为单位
```

### 2.2 PRPD数据结构

PRPD数据为二维结构:

```cpp
// PRPD数据定义
QVector<qint16> vecPrpdData;   // 实际存储结构 
// 尺寸 = PHASE_INTERVAL_CNT * QUANTIFICATION_AMP (通常为60*50=3000)

// 数据访问方式
qint16 getPrpdValue(int ampSectionIndex, int phaseIndex) {
    return vecPrpdData[ampSectionIndex * PHASE_INTERVAL_CNT + phaseIndex];
}
```

PRPD数据结构示意图:
```
PRPD[幅值区间][相位] = 计数

其中:
- 幅值区间: 0 ~ (QUANTIFICATION_AMP-1), 通常为50个区间
- 相位: 0 ~ (PHASE_INTERVAL_CNT-1), 通常为60个相位点(对应0°-360°)
- 计数: 特定相位点上、特定幅值区间内的放电次数
```

## 3. 转换原理与算法

### 3.1 数学映射关系

PRPS转PRPD的核心映射关系可以表示为:

$PRPD[a][p] = \sum_{i=0}^{n-1} f(PRPS[i][p], a)$

其中:
- $PRPD[a][p]$: 幅值区间a、相位p的PRPD值(计数)
- $PRPS[i][p]$: 周期i、相位p的PRPS值(幅值)
- $n$: 周期总数
- $f(v, a)$: 判断函数，当幅值v落在区间a内时为1，否则为0

### 3.2 幅值量化处理

幅值量化是将连续的幅值映射到离散区间的过程，核心代码:

```cpp
/**
 * @brief 将连续幅值量化到离散区间
 * @param ampValue 原始幅值
 * @param ampLower 幅值下限
 * @param ampUpper 幅值上限
 * @param quantizationAmpCnt 量化区间总数
 * @return 量化后的区间索引
 */
int32_t quantizationAmpSection(int32_t ampValue, float ampLower, float ampUpper, int32_t quantizationAmpCnt)
{
    // 计算每个区间的范围
    float rangePerSection = (ampUpper - ampLower) / static_cast<float>(quantizationAmpCnt);
    int sectionIndex = 0;
    float ampFloat = 0.0f;

    if(ampValue < Module::ZERO)
    {
        ampFloat = 0.0f;
    }
    else
    {
        ampFloat = static_cast<float>(ampValue);
    }
    // 确定幅值所属区间
    sectionIndex = static_cast<int>(ampFloat / rangePerSection);
    return sectionIndex;
}
```

量化处理的数学表达式:

$section = \frac{Amp - AmpLower}{AmpUpper - AmpLower} \times QuantizationAmpCnt$

其中:
- $section$: 幅值量化后的区间编号
- $Amp$: 原始幅值
- $AmpLower$, $AmpUpper$: 幅值下限和上限
- $QuantizationAmpCnt$: 量化区间总数

### 3.3 相位偏移处理

相位偏移用于校正测量数据中可能存在的相位差:

```cpp
// 相位偏移调整
int phaseShiftStep = mPhaseAlias / (360 / PRPSMapNS::PHASE_INTERVAL_CNT);
int newPhaseIndex = (phaseIndex + phaseShiftStep) % PRPSMapNS::PHASE_INTERVAL_CNT;
```

## 4. 详细实现流程

### 4.1 数据处理流程图

转换流程示意图:
```
PRPS数据采集
    ↓
数据预处理(幅值单位转换)
    ↓
周期迭代 → 相位迭代 → 幅值区间计算 → PRPD计数更新
    ↓
相位偏移校正
    ↓
PRPD数据保存
```

### 4.2 PRPS数据读取和处理

接收原始PRPS数据并进行处理的关键代码:

```cpp
/**
 * @brief 接收并处理PRPS数据
 * @param stData PRPS数据结构
 * @param userId 用户ID
 */
void UHFPRPSTestView::onDataRead(UHF::PRPSData stData, MultiServiceNS::USERID userId)
{
    if (getUserId() == INVALID_USER || getUserId() != userId || !isSampling()) {
        logError(QString("无效或不匹配的用户ID或采样状态: mUserId = %1, userId = %2, isSampling = %3")
            .arg(getUserId()).arg(userId).arg(isSampling()));
        return;
    }

    // 检查采样数据的有效性
    if (stData.vSpectrum.isEmpty()) {
        return;
    }

    mAmpLowerLimit = stData.cAmpLowerLimit;
    mAmpUpperLimit = stData.cAmpUpperLimit;

    // 根据单位选项转换幅度限制
    convertAmpLimit(mAmpLowerLimit, mAmpUpperLimit, mUnit);

    QVector<double> rawData;
    rawData.reserve(stData.vSpectrum.size());

    // 转换原始数据
    for (const auto& value : stData.vSpectrum) {
        double convertedValue = convertDB2DBM(value, mUnit);
        rawData.append(convertedValue);
    }
    
    // 设置数据到图表组件（内部会生成PRPD数据）
    mPtrChart->setData(rawData);
}
```

### 4.3 PRPD数据填充实现

将统计好的PRPD数据保存到数据规范中的代码:

```cpp
/**
 * @brief 填充PRPD数据到数据规范结构
 * @param pPRPDSpectrum PRPD谱数据指针
 */
void UHFPRPSTestView::fillPRPDData(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    // 计算PRPD数据总点数：相位数 × 幅值区间数
    int dataCnt = PRPSMapNS::PHASE_INTERVAL_CNT * PRPSMapNS::QUANTIFICATION_AMP;
    
    // 获取已统计的PRPD数据
    QVector<qint16> vecPrpdData = mPtrChart->prpdData();

    // 计算每个相位的幅值区间数
    int ampAreaCnt = vecPrpdData.size() / PRPSMapNS::PHASE_INTERVAL_CNT;
    
    // 计算相位偏移
    int phaseShiftStep = mPhaseAlias / (360 / PRPSMapNS::PHASE_INTERVAL_CNT);
    
    // 创建目标数据结构
    DataSpecificationNS::PRPDData stPRPDData;
    QVector<double> prpdData;
    prpdData.resize(dataCnt);
    
    // 重排数据，考虑相位偏移
    for(int phaseIdx = 0; phaseIdx < PRPSMapNS::PHASE_INTERVAL_CNT; ++phaseIdx)
    {
        for(int ampIdx = 0; ampIdx < ampAreaCnt; ++ampIdx)
        {
            int newPhaseIndex = (phaseIdx + phaseShiftStep) % PRPSMapNS::PHASE_INTERVAL_CNT;

            qint16 prpdValue = vecPrpdData.at(ampIdx*PRPSMapNS::PHASE_INTERVAL_CNT + phaseIdx);
            if(prpdValue < 0)
            {
                prpdValue = 0;
            }
            prpdData[newPhaseIndex*PRPSMapNS::QUANTIFICATION_AMP + ampIdx] = prpdValue;
        }
    }

    // 复制数据到二进制缓冲区
    stPRPDData.qbaPDSpectrumData.resize(prpdData.size() * sizeof(double));
    std::memcpy(stPRPDData.qbaPDSpectrumData.data(), prpdData.data(), stPRPDData.qbaPDSpectrumData.size());
    
    // 设置PRPD数据
    pPRPDSpectrum->setPRPDData(stPRPDData);
}
```

### 4.4 数据累积处理机制

PRPD数据的累积处理逻辑:

```cpp
/**
 * @brief 累积模式枚举
 */
enum class AccumulationMode
{
    ACCUMULATION = 0,      ///< 累积模式
    NOT_ACCUMULATION       ///< 非累积模式
};

/**
 * @brief 根据累积模式处理数据
 * @param mode 累积模式
 */
void PrpdViewItem::opByAccumulationMode(AccumulationMode mode)
{
    if(mode == AccumulationMode::ACCUMULATION)
    {
        // 保留已有数据，新数据累加
        // 在updatePrpdData中会累加计数
    }
    else // NOT_ACCUMULATION
    {
        // 清除现有数据，重新开始统计
        initPrpdVector(mPhaseCount, mPeriodCount);
    }
}

/**
 * @brief 设置PRPS数据并生成PRPD数据
 * @param prpsData PRPS数据
 */
void ChartWidget::setData(const QVector<double> &prpsData)
{
    // 存储PRPS数据
    mPrpsData = prpsData;
    
    // 根据累积模式处理
    if(mAccumulationMode == AccumulationMode::ACCUMULATION) {
        // 累积模式：保留已有PRPD数据，新数据累加
        updatePrpdData(prpsData);
    } else {
        // 非累积模式：清除已有PRPD数据，重新统计
        clearPrpdData();
        updatePrpdData(prpsData);
    }
    
    // 更新显示
    update();
}
```

## 5. 具体数据转换示例

### 5.1 示例PRPS数据

以下是一个简化的PRPS数据示例(3个周期，4个相位点):

```
PRPS数据:
相位→    0°    90°   180°  270°
周期1    25    0     65    0
周期2    0     45    0     32
周期3    30    0     55    0
```

### 5.2 转换过程详解

**步骤1: 确定幅值区间**
假设幅值范围为0-80dB，划分为4个区间:
- 区间1: 0-20dB
- 区间2: 20-40dB
- 区间3: 40-60dB
- 区间4: 60-80dB

**步骤2: 对每个相位点统计放电次数**

以0°相位为例:
```
周期1: 25dB → 区间2(20-40dB)
周期2: 0dB  → 无放电
周期3: 30dB → 区间2(20-40dB)

PRPD[区间2][0°] = 2
```

对所有相位点执行相同统计，得到PRPD数据:

```
PRPD结果:
相位→       0°    90°   180°  270°
60-80dB(4)  0     0     1     0
40-60dB(3)  0     1     1     0
20-40dB(2)  2     0     0     1
0-20dB(1)   0     0     0     0
```

**步骤3: 应用相位偏移**
假设相位偏移为30°，相当于1个相位点:

```
调整后的PRPD结果:
相位→       0°    90°   180°  270°
60-80dB(4)  0     1     0     0
40-60dB(3)  1     1     0     0
20-40dB(2)  0     0     1     2
0-20dB(1)   0     0     0     0
```

### 5.3 代码实现中的实际转换

在实际代码中，转换步骤是自动完成的:

1. PRPS数据通过`onDataRead`函数接收并预处理
2. 数据传递给图表组件的`setData`函数
3. 图表组件内部根据累积模式处理数据，并统计生成PRPD数据
4. 保存时通过`fillPRPDData`函数获取并处理PRPD数据

## 6. 数据保存和后续处理

### 6.1 保存流程

PRPS和PRPD数据一起保存到文件的代码:

```cpp
/**
 * @brief 保存测试数据到文件
 * @return 保存的文件路径
 */
QString UHFPRPSTestView::saveTestData()
{
    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("无法操作存储空间");
        return "";
    }
    diagDataInfo(true);

    auto pDataSpecification = std::make_unique<DataSpecificationNS::DataSpecification>();
    pDataSpecification->setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());

    // 处理PRPS数据
    auto pPRPSSpectrum = std::make_unique<DataSpecificationNS::PRPSSpectrum>();
    pPRPSSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_TEST);
    pPRPSSpectrum->setEquipmentName(mPtrSubTask->gapInfo(mGapId).s_strName);
    pPRPSSpectrum->setEquipmentCode(mPtrSubTask->gapInfo(mGapId).s_strId);
    pPRPSSpectrum->setTestPointName(mTestPoint.s_strPart);
    pPRPSSpectrum->setTestPointCode(mTestPoint.s_strId);
    pPRPSSpectrum->setSpectrumTypeCode(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPS);
    
    // 设置PRPS数据
    fillPRPSDataInfo(pPRPSSpectrum.get());
    fillPRPSData(pPRPSSpectrum.get());
    
    // 处理PRPD数据
    auto pPRPDSpectrum = std::make_unique<DataSpecificationNS::PRPDSpectrum>();
    pPRPDSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_TEST);
    pPRPDSpectrum->setEquipmentName(mPtrSubTask->gapInfo(mGapId).s_strName);
    pPRPDSpectrum->setEquipmentCode(mPtrSubTask->gapInfo(mGapId).s_strId);
    pPRPDSpectrum->setTestPointName(mTestPoint.s_strPart);
    pPRPDSpectrum->setTestPointCode(mTestPoint.s_strId);
    pPRPDSpectrum->setSpectrumTypeCode(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPD);
    
    // 设置PRPD数据
    fillPRPDDataInfo(pPRPDSpectrum.get());
    fillPRPDData(pPRPDSpectrum.get());

    // 添加到数据文件
    DataSpecificationNS::PRPSSpectrum* rawPtrPRPS = pPRPSSpectrum.get();
    DataSpecificationNS::PRPDSpectrum* rawPtrPRPD = pPRPDSpectrum.get();
    
    pDataSpecification->addSpectrum(rawPtrPRPS);
    pDataSpecification->addSpectrum(rawPtrPRPD);
    
    // 移交所有权给数据规范
    pPRPSSpectrum.release();
    pPRPDSpectrum.release();

    // 保存为二进制文件
    pDataSpecification->saveAsBinary(mSavedPath);
    
    return mSavedPath;
}
```

### 6.2 数据管理和诊断应用

保存的PRPD数据可用于:
- 放电类型识别
- 设备状态诊断
- 趋势分析
- 故障预警

## 7. 关键参数配置

### 7.1 主要参数列表

| 参数名 | 描述 | 典型值 |
|--------|------|--------|
| PHASE_INTERVAL_CNT | 相位间隔数 | 60 |
| QUANTIFICATION_AMP | 幅值量化等级数 | 50 |
| PERIOD_CNT | 周期数 | 50 |
| mPhaseAlias | 相位偏移值 | 0-359 |
| mUnit | 幅值单位 | UHF::UNIT_DB或UHF::UNIT_DBM |
| mAmpLowerLimit | 幅值下限 | 通常为0或负值 |
| mAmpUpperLimit | 幅值上限 | 通常为70-90dB |
| mAccumulationTime | 累积时间 | 单位:秒，默认60s |

### 7.2 参数配置接口

```cpp
/**
 * @brief 设置相位偏移
 * @param shift 相位偏移值(0-359)
 */
void setPrpdPhaseShift(int shift) {
    mPhaseAlias = shift;
}

/**
 * @brief 设置幅值范围
 * @param minValue 最小幅值
 * @param maxValue 最大幅值
 */
void setPrpdAmplitudeRange(double minValue, double maxValue) {
    mAmpLowerLimit = minValue;
    mAmpUpperLimit = maxValue;
}

/**
 * @brief 设置累积模式
 * @param mode 累积模式
 */
void setAccumulationMode(AccumulationMode mode) {
    mAccumulationMode = mode;
}

/**
 * @brief 设置累积时间
 * @param seconds 累积时间(秒)
 */
void setAccumulationTime(int seconds) {
    mAccumulationTime = seconds;
}
```

## 8. 性能考量与优化

### 8.1 数据处理优化

- 预分配内存减少动态分配开销
- 使用内存复制而非逐元素赋值
- 累积模式下仅更新变化部分

### 8.2 存储优化

- 量化压缩数据，减少存储空间需求
- 二进制存储提高I/O效率
- 缓存中间结果避免重复计算

## 9. 总结

PRPS到PRPD的转换是局部放电数据分析中的关键环节，实现了从时间序列采样到统计特征的转化。通过合理配置参数、精确实现转换算法并优化存储与处理性能，能够有效提取放电特征，为故障诊断提供可靠依据。

本文档详细说明了转换的数学原理、代码实现和数据示例，为实现和理解PRPS到PRPD的转换提供了全面参考。