#!/usr/bin/env python
# -*- coding: utf-8 -*-

import openpyxl
from openpyxl.styles import Alignment
import os
from datetime import datetime

def get_shanghai_time():
    """获取上海当前时间"""
    from datetime import datetime
    from zoneinfo import ZoneInfo
    shanghai_tz = ZoneInfo("Asia/Shanghai")
    return datetime.now(shanghai_tz)

def submit_week18_plan(excel_file):
    """
    更新Excel文件中的第18周工作计划并提交
    
    参数:
    excel_file -- Excel文件路径
    """
    try:
        print(f"正在读取文件: {excel_file}")
        
        # 检查文件是否存在
        if not os.path.exists(excel_file):
            print(f"错误: 文件不存在 '{excel_file}'")
            return False
            
        # 打开Excel文件
        workbook = openpyxl.load_workbook(excel_file)
        
        # 获取第18周工作计划表
        sheet_name = "2025年第18周工作计划表"
        if sheet_name not in workbook.sheetnames:
            print(f"错误: 未找到工作表 '{sheet_name}'")
            return False
        
        sheet = workbook[sheet_name]
        
        # 获取当前时间
        current_time = get_shanghai_time()
        formatted_date = current_time.strftime("%Y-%m-%d")
        
        # 第18周工作计划 - 个人实际工作计划
        plans = [
            {
                "序号": 1,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述": "UHF数据解析功能开发",
                "工作性质": "新开发",
                "目标": "完成UHF数据解析模块，支持PRPD图形显示",
                "重要性": "重要",
                "优先级": "高",
                "关键行动": "1. 分析UHF数据协议\n2. 实现数据解析模块\n3. 完成图形显示调试",
                "资源（人）": "段胜炜",
                "工时": 14,
                "计划完成日期": "2025-04-30",
                "备注": ""
            },
            {
                "序号": 2,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述": "蓝牙通信稳定性优化",
                "工作性质": "改进迭代",
                "目标": "提高弱信号环境下蓝牙连接稳定性",
                "重要性": "重要",
                "优先级": "高",
                "关键行动": "1. 优化重试策略\n2. 实现信号强度检测\n3. 改进用户反馈机制",
                "资源（人）": "段胜炜",
                "工时": 10,
                "计划完成日期": "2025-04-30",
                "备注": ""
            },
            {
                "序号": 3,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述": "PRPD图谱显示问题修复",
                "工作性质": "缺陷修复",
                "目标": "修复PRPD图谱显示的关键问题",
                "重要性": "重要",
                "优先级": "高",
                "关键行动": "1. 修复坐标轴标签错位问题\n2. 解决数据点颜色渐变问题\n3. 修复局部数据缺失问题",
                "资源（人）": "段胜炜",
                "工时": 12,
                "计划完成日期": "2025-04-30",
                "备注": ""
            },
            {
                "序号": 4,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述": "固件包功能测试",
                "工作性质": "测试验证",
                "目标": "完成V4.5.0固件包功能回归测试",
                "重要性": "重要",
                "优先级": "高",
                "关键行动": "1. 执行固件包回归测试\n2. 编写自测报告\n3. 完成版本发布准备",
                "资源（人）": "段胜炜",
                "工时": 8,
                "计划完成日期": "2025-04-28",
                "备注": ""
            },
            {
                "序号": 5,
                "项目名称": "PDS-Z200_T95",
                "类型": "产品项目",
                "小组": "嵌入式应用组",
                "工作概述": "云平台HTTPS功能完善",
                "工作性质": "改进迭代",
                "目标": "解决HTTPS登录功能稳定性问题",
                "重要性": "重要",
                "优先级": "中",
                "关键行动": "1. 修复SSL证书验证逻辑\n2. 完成证书链配置\n3. 实现长期解决方案",
                "资源（人）": "段胜炜",
                "工时": 6,
                "计划完成日期": "2025-05-01",
                "备注": ""
            }
        ]
        
        # 查找表头行
        header_row = None
        for row_idx, row in enumerate(sheet.iter_rows(min_row=1, max_row=20), 1):
            if any(cell.value == "序号" for cell in row if cell.value):
                header_row = row_idx
                break
        
        if header_row is None:
            print("错误: 未找到表头行")
            return False
        
        print(f"找到表头行: {header_row}")
        
        # 查找列索引
        columns = {}
        for cell in sheet[header_row]:
            if cell.value:
                # 处理长列名和短列名的映射
                col_name = cell.value
                if "工作概述" in col_name:
                    col_name = "工作概述"
                elif "目标" in col_name:
                    col_name = "目标"
                elif "工时" in col_name:
                    col_name = "工时"
                
                columns[col_name] = cell.column
                print(f"列 '{col_name}' 在位置 {cell.column}")
        
        # 检查必要的列是否存在
        required_columns = ["序号", "项目名称", "类型", "小组", "工作概述", "工作性质", 
                           "目标", "重要性", "优先级", "关键行动", "资源（人）", 
                           "工时", "计划完成日期"]
        missing_columns = [col for col in required_columns if col not in columns]
        if missing_columns:
            print(f"错误: 表格缺少以下列: {', '.join(missing_columns)}")
            return False
        
        # 找到开始插入数据的行
        start_row = header_row + 1
        while start_row <= sheet.max_row:
            cell = sheet.cell(row=start_row, column=columns["序号"])
            if not cell.value:
                break
            start_row += 1
        
        print(f"将从行 {start_row} 开始插入数据")
        
        # 插入数据
        for i, plan in enumerate(plans, 0):
            row_idx = start_row + i
            
            # 写入数据
            for col_name, col_idx in columns.items():
                if col_name in plan:
                    cell = sheet.cell(row=row_idx, column=col_idx)
                    # 如果是合并单元格，则跳过写入
                    if not isinstance(cell, openpyxl.cell.merged_cell.MergedCell):
                        cell.value = plan[col_name]
                        if col_name == "关键行动":
                            # 设置换行格式
                            cell.alignment = Alignment(wrap_text=True, vertical="top")
        
        # 在文件名中添加提交时间
        timestamp = current_time.strftime("%Y%m%d_%H%M%S")
        dir_name = os.path.dirname(excel_file)
        base_name = os.path.basename(excel_file)
        name_parts = os.path.splitext(base_name)
        new_file_name = f"{name_parts[0]}_提交_{timestamp}{name_parts[1]}"
        new_file_path = os.path.join(dir_name, new_file_name)
        
        # 保存Excel文件
        workbook.save(new_file_path)
        print(f"成功创建提交文件: {new_file_path}")
        
        # 在日报中记录提交信息
        with open("archived_reports/daily_report_2025_04.md", "a", encoding="utf-8") as f:
            f.write(f"\n\n## {formatted_date} 第18周工作计划提交\n\n")
            f.write(f"今日完成第18周工作计划提交，主要计划包括：\n")
            for plan in plans:
                f.write(f"- [{plan['工作性质']}] {plan['工作概述']}：{plan['目标']}，预计工时{plan['工时']}小时\n")
            f.write(f"\n提交文件：{new_file_name}\n")
            f.write(f"\n工作总结：完成了第18周工作计划的编制与提交，工作内容覆盖产品开发、缺陷修复、测试验证等多个方面，总计预估工时50小时。\n")
            
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        return False

if __name__ == "__main__":
    excel_file = r"File/20250425/QR-PDS-6.2-26WRPR-开发部-2025年第17周总结和2025年第18周计划-软件组.xlsx"
    result = submit_week18_plan(excel_file)
    if result:
        print("第18周工作计划提交成功！")
    else:
        print("第18周工作计划提交失败，请检查错误信息。") 