
## 一、项目概述

### 1.1 背景介绍

“最近在各产品的图谱数据传输/计算/展示过程中，均出现各种乱七八糟的问题，因此本周开始要求各产品均开始仔细核查数据的处理过程，从底层传输到软件开始，到软件上层各级流转，接口和数据规范均需要全部排查一遍。”

### 1.2 检查目的和范围

各产品均需要提交一份检查报告，含如下内容：

1.  原始数据的南向获取接口定义以及数据结构定义；
    
2.  数据经过了哪些计算，接口和数据结构的定义，输入输出；
    
3.  数据到界面的展示是什么组件，版本号是多少？组件提供绘图的接口定义和数据结构是什么；
    
4.  数据本地是如何保存的？原始数据还是经过什么计算后的数据；
    
5.  数据继续北向传输给了什么软件，接口和数据结构的定义是什么；
    
6.  数据流转过程中，是否存在写死相位数或者周期数等应该是动态参数的地方；
    
7.  其他跟数据流转相关的内容；
    

### 1.3 产品基本信息（版本号、部署环境等）

**固件包版本：**V4.4.0

### 1.4 术语和定义

下列术语和定义适用于本规范。

|  术语  |  英文缩写  |  定义  |
| --- | --- | --- |
|  局部放电(局放)  |  PD (partial discharge)  |  指设备绝缘系统中部分被击穿的电气放电，这种放电可以发生在导体(电极)附近，也可发生在其他位置。  |
|  带电检测  |  on-line detection  |  在不停电的情况下，对电力设备状况进行巡视性或针对性的状态检测。  |
|  高频  |  HF (high frequency)  |  指频率介于3MHz~30MHz（HF）区间的电磁波信号。  |
|  高频电流传感器  |  HFCT (high frequency current transformer)  |  用于将脉冲电流信号转换为电压信号的基于电磁感应原理的传感器。  |
|  特高频  |  UHF (ultra high frequency)  |  指频率介于300MHz~3000MHz的电磁波信号。  |
|  超声  |  AE (acoustic emission)  |  指频率高于20kHz的声波信号。超声传播分为空气传导和非空气传导两种方式。空气传导方式一般又称为空声，英文常用ultrasonic表示。非空气传导方式一般用宽频带超声传感器。  |
|  暂态地电压  |  TEV (transient earth voltage)  |  指局部放电脉冲在电器设备接地外壳包括接地线中激励的暂态电波信号序列。  |

## 二、AE（超声）技术数据流转

### 2.1 数据南向获取

#### 2.1.1 AE传感器规格及采集参数

AE（Acoustic Emission，超声）传感器是一种用于检测电力设备中放电产生的声波信号的设备，用于局部放电检测和定位。

|  参数  |  规格  |
| --- | --- |
|  频率范围  |  20kHz - 300kHz  |
|  频带选择  |  20-80kHz / 80-300kHz  |
|  增益档位  |  60dB / 80dB / 100dB  |
|  工作模式  |  幅值检测、PRPD、脉冲、波形  |
|  传感器通道  |  空声通道、表贴通道  |
|  同步源  |  内同步/电源同步/光同步/外同步  |
|  单位选择  |  dB / mV  |

#### 2.1.2 南向接口定义

**驱动基础接口：**

|  接口名称  |  参数  |  返回值  |  功能描述  |
| --- | --- | --- | --- |
|  get\_ae\_data  |  AEReadData \*pstAEReadData, AE\_CHANNEL eAE\_CHANNEL, AE\_CMD eAE\_CMD  |  INT32  |  获取AE原始数据  |
|  set\_ae\_settings  |  AEWorkSetForUp \*pstAEWorkSetForUp, AE\_CHANNEL eAE\_CHANNEL  |  INT32  |  设置AE工作参数  |
|  get\_ae\_connectstate  |  AE\_ConnectState \*peAE\_ConnectState  |  INT32  |  获取AE连接状态  |
|  Calibrate\_ae  |  float \*fValue, CalibrateAECmd eCalibrateAECmd  |  INT32  |  校准AE参数  |
|  get\_all\_ae\_views  |  AEAllView \*pstAEViewData, AE\_CHANNEL eAE\_CHANNEL  |  INT32  |  获取所有AE图谱数据  |

**AE通道枚举：**

```cpp
typedef enum
{
    AirSound = 0,    // 空声
    SurfaceMount,    // 表贴
    AE_Wireless,     // 无线通道(保留接口，暂不用)
} AE_CHANNEL;

```

**AE命令枚举：**

```cpp
typedef enum
{
    AE_OPEN_POWER = 0,   // 打开电源
    AE_START_SAMPLE,     // 开始采集
    AE_GET_DATA,         // 获取数据
    AE_STOP_SAMPLE,      // 停止采集
    AE_CLOSE_POWER,      // 关闭电源
} AE_CMD;

```

**AE工作模式枚举：**

```cpp
typedef enum
{
    Amplitude = 0,   // 幅值模式
    PRPD,            // PRPD模式
    Pulse,           // 脉冲模式
    Waveform,        // 波形模式
    AllViews,        // 一次上送所有图谱数据
    NoneMode,
} WorkMode;

```

**AE滤波器枚举：**

```cpp
typedef enum _AEFilter
{
    FILTER_20_80_kHZ = 0,    // 20-80kHz
    FILTER_80_300_kHZ = 1,   // 80-300kHz
    
    FILTER_MIN = FILTER_20_80_kHZ,
    FILTER_MAX = FILTER_80_300_kHZ,
    FILTER_DEFAULT = FILTER_20_80_kHZ
} AEFilter;

```

**AE增益等级枚举：**

```cpp
typedef enum _AEGainLevel
{
    AE_GAIN_LEVEL_60 = 0,    // 60dB增益
    AE_GAIN_LEVEL_80,        // 80dB增益
    AE_GAIN_LEVEL_100,       // 100dB增益
    AE_GAIN_LEVEL_INVALID = 0xFF,
} AEGainLevel;

```

#### 2.1.3 原始数据结构定义

**AE工作设置结构体：**

```cpp
typedef struct
{
    WorkMode eWorkMode;          // 工作模式
    AE_Unit eAE_Unit;            // 单位 dB/mV
    float fGain;                 // 增益
    SyncSource eSyncSource;      // 同步源(默认电源同步)
    UINT16 usFComponent;         // FFT所需的频率分量，有效值(10,20,30... 500)步长10Hz
    UINT8 ucNgrid;               // 波形图谱检测时显示的电网周期数据(1-10)
    float fAmplitudeThd;         // 幅值阈值
    float fPrpdThd;              // PRPD阈值
    float fPulseThd;             // 脉冲阈值
    float fWaveThd;              // 波形阈值
    UINT16 usPrpdBlocking;       // PRPD的关门时间
    UINT16 usPulseBlocking;      // 脉冲模式的关门时间
    UINT16 usPulseGating;        // 脉冲模式的开门时间
    AEFilter eAEFilter;          // AE带宽模式
} AEWorkSetForUp;

```

**AE读取数据结构体：**

```cpp
typedef struct
{
    SyncState eSyncState;        // 同步状态
    SyncSource eSyncSource;      // 同步源
    AE_CHANNEL eAE_CHANNEL;      // 传感器类型
    float fGain;                 // 增益
    UINT32 uiSamplingRate;       // 采样率
    UINT32 uiADRange;            // AD量程
    UINT8 ucADSampling;          // AD采样位数，例如13代表13位AD采样精度
    UINT8 ucADSampDataFormat;    // AD采样数据格式(0:标准二进制，1:二进制补码)
    WorkMode eWorkMode;          // 工作模式
    AE_Unit eAE_Unit;            // 单位 dB/mV
    UINT16 usPulseNum;           // 脉冲计数 单帧的脉冲数只有PRPD、脉冲模式有效
    union                        // 数据
    {
        struct
        {
            float fPeakValue;            // 峰值
            float fRMS;                  // 有效值
            float fFirstFreqComValue;    // 第一频率分量值
            float fScendFreqComValue;    // 第二频率分量值
        } AmpData;
        struct
        {
            float fPhaseValue;           // 相对相位值(0~360)
            float fPeakValue;            // 峰值
        } PRPDData[AEPULSEMAXNUM];
        struct
        {
            UINT32 uiPulseInterval;      // 脉冲间隔时间 us
            float fPeakValue;            // 峰值
        } PulseData[AEPULSEMAXNUM];
        struct
        {
            float faWaveValue[AEWAVENUM]; // 250点波形采集值
        } WaveData;
    } Data;
} AEReadData;

```

**AE所有视图数据结构体：**

```cpp
typedef struct _AEAllView
{
    SyncState eSyncState;            // 同步状态
    SyncSource eSyncSource;          // 同步源
    AE_CHANNEL eAE_CHANNEL;          // 传感器类型
    float fGain;                     // 增益
    UINT32 uiSamplingRate;           // 采样率
    UINT32 uiADRange;                // AD量程
    UINT8 ucADSampling;              // AD采样位数，例如13代表13位AD采样精度
    UINT8 ucADSampDataFormat;        // AD采样数据格式(0:标准二进制，1:二进制补码)
    WorkMode eWorkMode;              // 工作模式
    AE_Unit eAE_Unit;                // 单位 dB/mV
    UINT16 usPhasePointNum;          // 数据中的相位图谱点数
    UINT16 usPulsePointNum;          // 数据中的脉冲图谱点数
    UINT16 usWavePointNum;           // 数据中的波形图谱点数

    AEAmpData stAeAmp;               // 幅值数据
    AEPhaseData staPhaseData[MAX_PHASE_PULSE_NUM_PER_MSG];  // 相位数据
    AEPulseData staPulseData[MAX_PHASE_PULSE_NUM_PER_MSG];  // 脉冲数据
    float faWaveData[MAX_WAVE_POINT_PER_MSG];               // 波形数据
} AEAllView;

```

**应用层AE数据结构：**

#### 2.1.4 数据获取实际流程

AE数据获取流程遵循以下步骤：
![[Pasted image 20250520140833.png]]
    

**核心数据获取代码示例：**

**AE数据处理日志示例：**

幅值图谱：
![[Pasted image 20250520145758.png]]
相位图谱：
![[Pasted image 20250520150854.png]]
飞行图谱：
![[Pasted image 20250520150937.png]]
波形图谱数据：
![[Pasted image 20250520154526.png]]
全图谱数据：
![[Pasted image 20250520161009.png]]
### 2.3 界面展示与存储

1.  本地数据存储方式
	1. dat：二进制格式，适配《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》、《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》两种协议
	2. txx：xml格式，适配《t95内部数据文件格式_评审稿.docx》《带电检测巡检任务数据格式规范V1.0.1.docx》
2.  北向传输接口定义
	1. PDST桌面软件：
		1. AE 幅值检测数据（图谱）
			 参考 《t95内部数据文件格式_评审稿.docx》 中4.1节
		2. AE 相位检测数据
			 参考《t95内部数据文件格式_评审稿.docx》中4.2节
		3. AE 飞行检测数据
			 参考 《t95内部数据文件格式_评审稿.docx》 中4.3节
		4. AE 波形检测数据
		     参考 《t95内部数据文件格式_评审稿.docx》 中4.4节
		5. AE 幅值检测数据（非图谱）
			 参考 《t95内部数据文件格式_评审稿.docx》 中4.5节
	2. 智能巡检：
		1. AE 幅值检测数据（图谱）
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.5.1节
		2. AE 相位检测数据
			 参考《带电检测巡检任务数据格式规范V1.0.1.docx》中3.5.2节
		3. AE 飞行检测数据
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.5.3节
		4. AE 波形检测数据
		     参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.5.4节
		5. AE 幅值检测数据（非图谱）
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.5.5节
	3. 接入终端：
		1. 公司规范：
			1. AE 幅值检测数据（图谱）
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.1节
		2. AE 相位检测数据
			 参考《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》中5.4.3.2节
		3. AE 飞行检测数据
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.3节
		4. AE 波形检测数据
		     参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.4节
		5. AE 幅值检测数据（非图谱）
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.5节
	4. 国网规范：
		1. AE 幅值检测数据（图谱）
			 参考 《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》 中5.6节(超声波局放图谱数据格式)
		2. AE 相位检测数据
			 参考《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》中5.6节(超声波相位图数据格式)
		3. AE 飞行检测数据
			 参考 《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》 中5.6节(超声波脉冲图数据格式)
		4. AE 波形检测数据
		     参考 《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》 中5.6节(超声波时域波形图数据格式)
    
## 四、UHF（特高频）技术数据流转

### 4.1 数据南向获取

#### 4.1.1 UHF传感器规格及南向接口

UHF（Ultra High Frequency，特高频）传感器是一种用于检测电力设备中局部放电产生的高频电磁波信号的设备，广泛应用于GIS设备内部放电检测。

|  参数  |  规格  |
| --- | --- |
|  频率范围  |  300MHz - 3GHz  |
|  滤波方式  |  全通/低通/高通  |
|  增益设置  |  0dB / 20dB  |
|  工作模式  |  幅值检测、PRPS、PRPD  |
|  同步方式  |  内同步/电源同步/光同步/外同步  |
|  通信方式  |  无线Zigbee通信  |
|  数据版本  |  V1(INT8型60点)/V2(float型最大360点)  |

#### 4.1.2 南向接口定义

**驱动基础接口：**

|  接口名称  |  参数  |  返回值  |  功能描述  |
| --- | --- | --- | --- |
|  get\_uhfhfct\_data  |  UHFHFCTData \*pstUHFHFCTData, WLDeviceType eDeviceType  |  INT32  |  获取UHF原始数据  |
|  set\_uhf\_filter  |  UHFFilterControl eFilterControl  |  INT32  |  设置UHF滤波方式  |
|  set\_uhfhfct\_gain  |  UHFHFCTGain eUHFHFCTGain, WLDeviceType eDeviceType  |  INT32  |  设置UHF增益  |
|  set\_wireless\_syncsource  |  SyncSource eUHFHFCTSync, WLDeviceType eDeviceType  |  INT32  |  设置同步源  |
|  start\_search\_wireless  |  WLDeviceType eDeviceType  |  INT32  |  开始搜索无线UHF设备  |
|  get\_wireless\_addrlist  |  WLDeviceAddr \*pstAddr, UINT16 \*pusAddrNum, WLDeviceType eDeviceType  |  INT32  |  获取设备地址列表  |
|  set\_wireless\_addrlist  |  WLDeviceAddr \*pstAddr, WLDeviceType eDeviceType  |  INT32  |  设置设备地址  |

**滤波方式枚举：**

```cpp
typedef enum
{
    ALL_PASS_FILTER = 0, // 全通滤波
    LOW_PASS_FILTER,     // 低通滤波
    HIGH_PASS_FILTER,    // 高通滤波
} UHFFilterControl;

```

**增益设置枚举：**

```cpp
typedef enum
{
    GAIN_20 = 0, // 20dB增益 前置增益开
    GAIN_0,      // 0dB增益  前置增益关
    // 下面是HFCT特有的
    ATTENUATE_0, // 0dB衰减
    ATTENUATE_20, // -20dB衰减
    ATTENUATE_40, // -40dB衰减
    ATTENUATE_60, // -60dB衰减
} UHFHFCTGain;

```

**同步源枚举：**

```cpp
typedef enum
{
    INTER_SYNC = 0,   //: 内同步
    WIRELESS_SYNC,    // 电源同步
    LIGHT_SYNC,       // 光同步
    OUTER_SYNC        // 外同步
} SyncSource;

```

#### 4.1.3 原始数据结构与定义

**UHF原始数据结构体：**

```cpp
typedef struct
{
    UHFFilterControl eBandWidth;    // 带宽 UHF独有
    SpectrumState eSpectrumState;   // 频谱状态 HFCT独有
    SyncSource eSyncSource;         // 同步源
    SyncState eSyncState;           // 同步状态
    INT8 cMaxSpectrum;              // 转换后的最大频谱
    INT8 cGain;                     // 增益
    INT8 caSpectrum[SPECTTRUMNUM];  // 转换后的频谱值，当cDataVersion=1时有效

    INT8 cDataVersion;              // 图谱数据格式版本：DATA_VERSION_V1：老版本数据格式；DATA_VERSION_V2：新版本数据格式
    
    float fPrpsDataPerGridPeriod[MAX_PHASE_NUM]; // 转换后的频谱值（每周波prps图谱数据），当cDataVersion>=DATA_VERSION_V2时有效
    float fMaxSpectrum;             // 转换后的最大频谱值，当cDataVersion=DATA_VERSION_V2时有效
    UINT16 usPhaseNum;              // fSpectrum数组内实际上送的每周波Prps相位点数
    INT8 cAmpUpperLimit;            // 幅值上限
    INT8 cAmpLowerLimit;            // 幅值下限
} UHFHFCTData;

```

**频谱状态枚举：**

```cpp
typedef enum
{
    SPECTRUM_INSIDE_RANGE = 0, // 频谱范围内
    SPECTRUM_UNDER_LOW,        // 频谱小于范围
    SPECTRUM_ABOVE_HIGH,       // 频谱大于范围
} SpectrumState;

```

**同步状态枚举：**

```cpp
typedef enum
{
    Not_Sync = 0, // 未同步
    Synced,       // 已同步
} SyncState;

```

#### 4.1.4 UHF数据获取流程

UHF数据获取流程遵循以下步骤：

![[Pasted image 20250520171847.png]]

**核心数据获取代码示例：**

**UHF数据获取日志示例：**
![[Pasted image 20250520141458.png]]

### 4.2 数据处理类型与流转

#### 4.2.1 UHF数据流转总体流程

![[Pasted image 20250520172329.png]]

#### 4.2.2 UHF幅值检测数据流转

![[Pasted image 20250520170802.png]]

#### 4.2.3 UHF PRPS检测数据流转
![[Pasted image 20250520171011.png]]


#### 4.2.5 UHF周期检测数据流转

![[Pasted image 20250520170857.png]]
    

### 4.3 界面展示与存储

1. 本地数据存储方式
	1. dat：二进制格式，适配《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》、《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》两种协议
	2. txx：xml格式，适配《t95内部数据文件格式_评审稿.docx》《带电检测巡检任务数据格式规范V1.0.1.docx》
2.  北向传输接口定义
	1. PDST桌面软件：
		1. UHF幅值检测数据
			 参考 《t95内部数据文件格式_评审稿.docx》 中3.1节
		2. UHF PRPS检测数据
			 参考《t95内部数据文件格式_评审稿.docx》中3.2节
		3. UHF PRPD检测数据
			 参考 《t95内部数据文件格式_评审稿.docx》 中3.3节
		4. UHF周期检测数据
		     参考 《t95内部数据文件格式_评审稿.docx》 中3.4节
	2. 智能巡检：
		1. UHF幅值检测数据
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.4.1节
		2. UHF PRPS检测数据
			 参考《带电检测巡检任务数据格式规范V1.0.1.docx》中3.4.2节
		3. UHF PRPD检测数据
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.4.3节
		4. UHF周期检测数据
		     参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.4.4节
	3. 接入终端：
		1. 公司规范：
			1. 特高频PRPD图
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.2.1节
			2. 特高频PRPS图
			 参考《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》中5.4.2.2节
			3. 特高频峰值统计图
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.2.3节
			4. 特高频波形图
		     参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.2.4节
			5. 特高频周期图
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.5节
			6. 特高频幅值图
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.6节
		2. 国网规范：
			1. PRPD 图和 PRPS 图数据格式
			 参考 《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》 中5.3节(表6)
    

## 五、HFCT（高频电流互感器）技术数据流转

### 5.1 数据南向获取

#### 5.1.1 HFCT传感器规格及南向接口

HFCT（High Frequency Current Transformer，高频电流互感器）是一种用于检测电力设备局部放电产生的高频电流脉冲信号的传感器，主要应用于电缆和变压器局部放电检测。

|  参数  |  规格  |
| --- | --- |
|  频率范围  |  3MHz - 30MHz  |
|  衰减设置  |  0dB / -20dB / -40dB / -60dB  |
|  工作模式  |  幅值检测、PRPS、PRPD  |
|  同步方式  |  内同步/电源同步/光同步/外同步  |
|  通信方式  |  无线Zigbee通信  |
|  数据版本  |  V1(INT8型60点)/V2(float型最大360点)  |
|  安装方式  |  卡环式、夹钳式  |

#### 5.1.2 南向接口定义

**驱动基础接口：**

|  接口名称  |  参数  |  返回值  |  功能描述  |
| --- | --- | --- | --- |
|  get\_uhfhfct\_data  |  UHFHFCTData \*pstUHFHFCTData, WLDeviceType eDeviceType  |  INT32  |  获取HFCT原始数据  |
|  set\_uhfhfct\_gain  |  UHFHFCTGain eUHFHFCTGain, WLDeviceType eDeviceType  |  INT32  |  设置HFCT衰减  |
|  set\_wireless\_syncsource  |  SyncSource eUHFHFCTSync, WLDeviceType eDeviceType  |  INT32  |  设置同步源  |
|  start\_search\_wireless  |  WLDeviceType eDeviceType  |  INT32  |  开始搜索无线HFCT设备  |
|  get\_wireless\_addrlist  |  WLDeviceAddr \*pstAddr, UINT16 \*pusAddrNum, WLDeviceType eDeviceType  |  INT32  |  获取设备地址列表  |
|  set\_wireless\_addrlist  |  WLDeviceAddr \*pstAddr, WLDeviceType eDeviceType  |  INT32  |  设置设备地址  |

**设备类型枚举：**

```cpp
typedef enum{
    UHF_TERMINAL = 0, // UHF终端
    HFCT_TERMINAL,    // HFCT终端
    SYNCHRONIZER,     // 同步器
    RECEIVER,         // 接收器
    EXTERNAL_DEVICE,  // 外部设备
    AE_TERMINAL,      // 超声波
    TEV_TERMINAL,     // 地电波
    CABLE_CURRENT_TERMINAL, // 电缆电流测量终端
    MAX_TERMINAL_NUM,
    NODEVICEType = 0xff, // 初始值
} WLDeviceType;

```

**HFCT衰减设置枚举：**

```cpp
typedef enum
{
    GAIN_20 = 0,      // 20dB增益 (UHF专用)
    GAIN_0,           // 0dB增益  (UHF专用)
    ATTENUATE_0,      // 0dB衰减  (HFCT专用)
    ATTENUATE_20,     // -20dB衰减(HFCT专用)
    ATTENUATE_40,     // -40dB衰减(HFCT专用)
    ATTENUATE_60,     // -60dB衰减(HFCT专用)
} UHFHFCTGain;

```

**同步源枚举：**

```cpp
typedef enum
{
    INTER_SYNC = 0,   // 内同步
    WIRELESS_SYNC,    // 电源同步
    LIGHT_SYNC,       // 光同步
    OUTER_SYNC        // 外同步
} SyncSource;

```

#### 5.1.3 原始数据结构与定义

**HFCT原始数据结构体：** HFCT与UHF共用数据结构，但部分字段具有不同含义

```cpp
typedef struct
{
    UHFFilterControl eBandWidth;    // 带宽 UHF独有
    SpectrumState eSpectrumState;   // 频谱状态 HFCT独有
    SyncSource eSyncSource;         // 同步源
    SyncState eSyncState;           // 同步状态
    INT8 cMaxSpectrum;              // 转换后的最大频谱
    INT8 cGain;                     // 增益 HFCT增益数据为0~60对应菜单0~-60
    INT8 caSpectrum[SPECTTRUMNUM];  // 转换后的频谱值，当cDataVersion=1时有效

    INT8 cDataVersion;              // 图谱数据格式版本：DATA_VERSION_V1：老版本数据格式；DATA_VERSION_V2：新版本数据格式
    
    float fPrpsDataPerGridPeriod[MAX_PHASE_NUM]; // 转换后的频谱值（每周波prps图谱数据），当cDataVersion>=DATA_VERSION_V2时有效
    float fMaxSpectrum;             // 转换后的最大频谱值，当cDataVersion=DATA_VERSION_V2时有效
    UINT16 usPhaseNum;              // fSpectrum数组内实际上送的每周波Prps相位点数
    INT8 cAmpUpperLimit;            // 幅值上限
    INT8 cAmpLowerLimit;            // 幅值下限
} UHFHFCTData;

```

**频谱状态枚举：**

```cpp
typedef enum
{
    SPECTRUM_INSIDE_RANGE = 0, // 频谱范围内
    SPECTRUM_UNDER_LOW,        // 频谱小于范围
    SPECTRUM_ABOVE_HIGH,       // 频谱大于范围
} SpectrumState;

```

**同步状态枚举：**

```cpp
typedef enum
{
    Not_Sync = 0, // 未同步
    Synced,       // 已同步
} SyncState;

```
#### 5.1.4 数据获取流程

HFCT数据获取流程遵循以下步骤：
同4.1.4 UHF数据获取流程
### 5.2 数据处理类型与流转

#### 5.2.1 HFCT数据流转总体流程

![[Pasted image 20250520172440.png]]

#### 5.2.2 HFCT幅值检测数据流转

![[Pasted image 20250520165558.png]]
#### 5.2.3 HFCT 周期检测数据流转

![[Pasted image 20250520165726.png]]
#### 5.2.4 HFCT PRPS检测数据流转

![[Pasted image 20250520165833.png]]
    

**处理日志示例：**

![[Pasted image 20250520141458.png]]
### 5.3 界面展示与存储

#### 5.3.1 HFCT PRPS数据展示组件与接口

![[Pasted image 20250520170102.png]]

#### 5.3.2 HFCT数据存储结构

1. 本地数据存储方式
	1. dat：二进制格式，适配《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》、《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》两种协议
	2. txx：xml格式，适配《t95内部数据文件格式_评审稿.docx》《带电检测巡检任务数据格式规范V1.0.1.docx》
2.  北向传输接口定义
	1. PDST桌面软件：
		1. HFCT幅值检测数据
			 参考 《t95内部数据文件格式_评审稿.docx》 中2.1节
		2. HFCT PRPS检测数据
			 参考《t95内部数据文件格式_评审稿.docx》中2.2节
		3. HFCT PRPD检测数据
			 参考 《t95内部数据文件格式_评审稿.docx》 中2.3节
		4. HFCT 周期检测数据
		     参考 《t95内部数据文件格式_评审稿.docx》 中2.4节
	2. 智能巡检：
		1. HFCT幅值检测数据
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.5.1节
		2. HFCT PRPS检测数据
			 参考《带电检测巡检任务数据格式规范V1.0.1.docx》中3.5.2节
		3. HFCT PRPD检测数据
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.5.3节
		4. HFCT 周期检测数据
		     参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.5.4节
		5. 高频脉冲波形检测数据
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.5.5节
	3. 接入终端：
		1. 公司规范：
			1. AE 幅值检测数据（图谱）
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.1节
		2. AE 相位检测数据
			 参考《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》中5.4.3.2节
		3. AE 飞行检测数据
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.3节
		4. AE 波形检测数据
		     参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.4节
		5. AE 幅值检测数据（非图谱）
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.3.5节
	4. 国网规范：
		1. AE 幅值检测数据（图谱）
			 参考 《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》 中5.6节(超声波局放图谱数据格式)
## 六、TEV（暂态低电压）技术数据流转

### 6.1 数据南向获取

#### 6.1.1 TEV传感器规格及采集参数

TEV（Transient Earth Voltage，暂态低电压）传感器是一种用于检测电力设备表面局部放电产生的电磁波信号的设备。

|  参数  |  规格  |
| --- | --- |
|  检测范围  |  0~60dB  |
|  检测通道  |  三个通道(0dB/20dB/40dB)  |
|  增益方式  |  分档增益  |
|  工作模式  |  幅值检测、脉冲计数、PRPS  |
|  采样精度  |  16位  |
|  输出格式  |  数字输出  |
|  校准支持  |  支持零漂和系数校准  |

#### 6.1.2 南向接口定义

**驱动基础接口：**

| 接口名称                       | 参数                                               | 返回值   | 功能描述                  |
| -------------------------- | ------------------------------------------------ | ----- | --------------------- |
| get\_tev\_data             | TEVReadData \*pstTEVReadData, TEV\_CMD eTEV\_CMD | INT32 | 获取TEV原始数据，包含不同模式下的数据  |
| set\_tev\_settings         | TEVWorkSettings \*pstTEVWorkSettings             | INT32 | 设置TEV工作参数，如工作模式、报警阈值等 |
| get\_tev\_calibrate\_param | TEVCalibrateParam \*pstTEVCalibrateParam         | INT32 | 获取TEV校准参数             |
| Calibrate\_tev             | TevCaliCommand eCmd, AutoCali \*pstCali          | INT32 | 执行TEV校准命令             |
| set\_tev\_cali\_coeff      | TevCaliCommand eCmd, float fK, float fB          | INT32 | 设置TEV校准系数             |

**TEV命令参数枚举：**

```cpp
typedef enum {
    TEV_OPEN_POWER = 0, // 打开电源
    TEV_GET_DATA = 2,   // 获取数据
    TEV_CLOSE_POWER = 4 // 关闭电源
} TEV_CMD;

```

**TEV工作模式枚举：**

```cpp
typedef enum _TEVWorkMode {
    MODE_AMP = 0,   // 幅值检测模式
    MODE_PULSE,     // 脉冲计数模式
    MODE_PRPS,      // PRPS模式
    MODE_COUNT
} TEVWorkMode;

```

**TEV通道枚举：**

```cpp
typedef enum _TevChannel {
    TEV_CHANNEL_0dB = 0,  // 0~19dB
    TEV_CHANNEL_20dB,     // 20~39dB
    TEV_CHANNEL_40dB,     // 40~60dB
    TEV_CHANNEL_COUNT
} TevChannel;

```

#### 6.1.3 原始数据结构定义

**TEV工作设置结构体：**

```cpp
typedef struct {
    UINT8 ucWorkMode;    // 工作模式 0幅值检测 1脉冲模式 2 PRPS模式
    UINT8 ucTime;        // 脉冲计数时间 1,2 单位秒
    UINT8 ucBackGround;  // TEV黄色报警阈值
    UINT8 ucAlarm;       // TEV红色报警阈值
} TEVWorkSettings;

```

**TEV读取数据结构体：**

```cpp
typedef struct {
    UINT8 ucWorkMode;    // 工作模式 0幅值检测 1脉冲模式 2 PRPS模式
    union {              // 数据
        struct {
            INT8 cAmpValue;    // 幅值 当前测量值 -1~61 -1表示<0dB 61表示>60dB
            UINT16 usTevMv;    // tev当前档位db值对应的mv值
        } AmpData;
        struct {
            UINT32 uiPulseNum;     // 脉冲数
            UINT32 uiPerPulseNUm;  // 每周期脉冲数
            UINT32 uiPDSeverity;   // 放电严重程度
            INT8 cAmpValue;        // 幅值测量值 -1~61 -1表示<0dB 61表示>60dB
        } PulseData;
        struct {
            INT8 cMaxSpectrum;      // 转换后的最大频谱
            SpectrumState eSpectrumState; // 频谱增益状态
            INT8 cSpectrum[TEV_PRPS_COUNT]; // 转换后的频谱值
        } PrpsData;
    } Data;
} TEVReadData;

```

**TEV校准参数结构体：**

```cpp
typedef struct _CalibrateParam {
    float fK;           // 系数
    float fB;           // 零漂
    CaliStatus eCaliSta; // 校准状态
} CalibrateParam;

typedef struct _TEVCalibrateParam {
    CalibrateParam CaliParam[TEV_CHANNEL_COUNT];
    UINT16 ausDacRef[TEV_CHANNEL_COUNT][20]; // tev 三个采集通道DAC码值参考数组
} TEVCalibrateParam;

```

#### 6.1.4 数据获取实际流程
TEV 幅值检测数据流转：
![[Pasted image 20250520174256.png]]
TEV 脉冲检测数据流转：
![[Pasted image 20250520174420.png]]
TEV PRPS检测数据流转：
![[Pasted image 20250520174525.png]]

**数据获取日志示例：**
TEV幅值图谱
![[Pasted image 20250520155019.png]]
TEV脉冲图谱：
![[Pasted image 20250520155105.png]]
TEVPRPS图谱：
因为无法打开TEV PRPS，所以无日志文件
#### 6.3.2 TEV数据存储结构
1. 本地数据存储方式
	1. dat：二进制格式，适配《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》、《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》两种协议
	2. txx：xml格式，适配《t95内部数据文件格式_评审稿.docx》《带电检测巡检任务数据格式规范V1.0.1.docx》
2.  北向传输接口定义
	1. PDST桌面软件：
		1. TEV幅值检测数据
			 参考 《t95内部数据文件格式_评审稿.docx》 中5.1节
		2. TEV脉冲检测数据
			 参考《t95内部数据文件格式_评审稿.docx》中5.2节
		3. TEV PRPS检测数据
			 参考 《t95内部数据文件格式_评审稿.docx》 中5.3节
		4. TEV PRPD检测数据
		     参考 《t95内部数据文件格式_评审稿.docx》 中5.4节
	2. 智能巡检：
		1. TEV幅值检测数据
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.6.1节
		2. TEV脉冲检测数据
			 参考《带电检测巡检任务数据格式规范V1.0.1.docx》中3.6.2节
		3. TEV PRPS检测数据
			 参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.6.3节
		4. TEV PRPD检测数据
		     参考 《带电检测巡检任务数据格式规范V1.0.1.docx》 中3.6.4节
	3. 接入终端：
		1. 公司规范：
			1. 暂态地电压幅值
			 参考 《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》 中5.4.4.1节
			2. 暂态地电压幅值图
			 参考《电力设备局部放电图谱数据通用格式规范_V4.1.1.0_20211029_华乘内部版.docx》中5.4.4.2节
		2. 国网规范：
			1. TEV 幅值检测数据
			 参考 《设备监控〔2022〕133号　电气设备数字化测试仪器数据与通信技术规范》 中5.7节(暂态地电压局放图谱数据格式)
## 七、附录

### 7.1 完整接口文档

### 7.2 数据结构详细定义

### 7.3 关键代码片段

### 7.4 测试用例及结果

### 7.5 日志样本

## 报告编写说明

此报告需要按照以下要求进行编写：

1.  使用时序图表述交互逻辑
    
2.  用表格或者描述性文字表述数据结构
    
3.  重点内容需要打印出日志来
    
4.  截取相应代码备查