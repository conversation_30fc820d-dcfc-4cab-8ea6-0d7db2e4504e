# 文件命名与路径规范指南

为确保项目文件结构清晰、统一，特制定本规范指南。所有团队成员在创建、修改、存储文件时，需严格遵循以下规则。

## 一、文件目录结构

```
├── archived_reports/                  # 存放所有报告类文档
│   ├── file_structure_guideline.md    # 文件命名和路径规范指南
│   ├── 2024/                          # 2024年报告
│   │   ├── daily/                     # 日报目录
│   │   │   └── daily_report_2024_MM.md
│   │   ├── weekly/                    # 周报目录
│   │   │   ├── weekly_plan_2024_MM.md
│   │   │   └── weekly_report_2024_MM.md
│   │   └── monthly/                   # 月报目录
│   │       ├── monthly_report_2024_MM.md
│   │       └── monthly_summary_2024_MM.md
│   └── 2025/                          # 2025年报告
│       ├── daily/                     # 日报目录
│       │   └── daily_report_2025_MM.md
│       ├── weekly/                    # 周报目录
│       │   ├── weekly_plan_2025_MM.md
│       │   └── weekly_report_2025_MM.md
│       └── monthly/                   # 月报目录
│           ├── monthly_report_2025_MM.md
│           └── monthly_summary_2025_MM.md
├── config/                            # 配置文件和脚本
│   └── get_shanghai_time.py           # 获取上海时间脚本
└── File/                              # 工作文档（分层结构）
    ├── 2024/                          # 年份目录
    │   └── MM/                        # 月份目录
    │       └── YYYYMMDD/              # 日期目录（工作文档）
    └── 2025/                          # 年份目录
        └── MM/                        # 月份目录
            └── YYYYMMDD/              # 日期目录（工作文档）
```

## 二、文件命名规则

### 1. 日报文件
- **命名格式**：`daily_report_YYYY_MM.md`
- **存放位置**：`archived_reports/YYYY/daily/`目录
- **示例**：`archived_reports/2025/daily/daily_report_2025_04.md`（2025年4月日报）

### 2. 周计划文件
- **命名格式**：`weekly_plan_YYYY_MM.md`
- **存放位置**：`archived_reports/YYYY/weekly/`目录
- **示例**：`archived_reports/2025/weekly/weekly_plan_2025_04.md`（2025年4月周计划）

### 3. 周报文件
- **命名格式**：`weekly_report_YYYY_MM.md`
- **存放位置**：`archived_reports/YYYY/weekly/`目录
- **示例**：`archived_reports/2025/weekly/weekly_report_2025_04.md`（2025年4月周报）

### 4. 月报文件
- **命名格式**：`monthly_report_YYYY_MM.md`
- **存放位置**：`archived_reports/YYYY/monthly/`目录
- **示例**：`archived_reports/2025/monthly/monthly_report_2025_04.md`（2025年4月月报）

### 5. 月度总结文件
- **命名格式**：`monthly_summary_YYYY_MM.md`
- **存放位置**：`archived_reports/YYYY/monthly/`目录
- **示例**：`archived_reports/2025/monthly/monthly_summary_2025_04.md`（2025年4月月度总结）

### 6. 工作文档
- **存放位置**：`File/YYYY/MM/YYYYMMDD/`目录
- **示例**：`File/2025/04/20250415/项目方案.md`

## 三、文件管理规则

### 1. 创建新文件
- 新建报告类文件时，必须遵循上述命名规则和存放位置
- 严禁在根目录下创建临时报告文件
- 每月初需要创建新的月度文件，不要在旧文件中追加新月份的内容
- 每年初需要创建新的年度目录结构

### 2. 文件更新
- 日报、周报、月报等文件应按时更新
- 更新时应保持原有格式，确保内容一致性
- 文件中的日期信息应与实际日期一致

### 3. 文件归档
- 每月底应核对当月所有文件是否完整
- 确保所有报告类文件都按照规定格式存放在正确位置
- 新月度开始前，检查上个月的文件是否已妥善归档
- 年度切换时，确保所有文件都归档到对应年份的目录下

## 四、常见问题与解决方案

### 问题1：发现重复文件
- **解决方案**：比较内容，保留最新或最完整的版本，删除重复文件

### 问题2：文件命名不规范
- **解决方案**：按照本规范重命名文件，确保符合统一标准

### 问题3：找不到特定文件
- **解决方案**：按照本规范中的目录结构查找，确认文件命名是否符合规范

## 五、规范执行与责任

- 所有团队成员必须严格遵循本规范
- 发现不符合规范的文件，应及时纠正
- 定期检查文件结构，确保持续符合规范要求

**本规范自发布之日起执行，如有更新将另行通知。**

*最后更新日期：2025年4月28日* 