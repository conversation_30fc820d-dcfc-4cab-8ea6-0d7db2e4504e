from datetime import datetime
import pytz

# 获取上海时间
def get_shanghai_time():
    try:
        # 获取当前UTC时间
        utc_now = datetime.utcnow()
        # 定义上海时区
        shanghai_tz = pytz.timezone('Asia/Shanghai')
        # 将UTC时间转换为上海时间
        shanghai_time = utc_now.replace(tzinfo=pytz.utc).astimezone(shanghai_tz)
        return shanghai_time
    except Exception as e:
        print(f"获取上海时间出错: {e}")
        # 无法获取上海时间时，返回本地时间
        return datetime.now()

if __name__ == "__main__":
    # 获取上海时间
    shanghai_time = get_shanghai_time()
    # 格式化显示
    formatted_time = shanghai_time.strftime("%Y-%m-%d %H:%M:%S")
    print(formatted_time)
    # 返回年月日单独的值，方便后续使用
    print(f"年: {shanghai_time.year}")
    print(f"月: {shanghai_time.month}")
    print(f"日: {shanghai_time.day}") 