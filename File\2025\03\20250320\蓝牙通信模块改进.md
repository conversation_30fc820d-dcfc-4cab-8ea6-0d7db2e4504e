# 蓝牙通信模块改进文档

## 1. 问题描述

在蓝牙通信模块中发现数据写入存在可靠性问题，具体表现为：

1. 某些情况下只有部分数据被成功写入（如调用写入4000字节但实际只写入了1007字节）
2. 写入函数返回0（没有数据被写入）时没有合适的处理机制
3. 连接状态检测机制不够健壮，仅依赖静态标志

这些问题可能导致数据传输不完整、通信失败但没有正确报错，影响系统稳定性和可靠性。

## 2. 排查过程

### 2.1 底层写入函数分析

首先分析了`Bluetooth::writeData`函数的实现：

```cpp
int Bluetooth::writeData(int portHandle, const char *buffer, int dataLen)
{
    int res = -1; // 默认返回值为-1
    uint timeout = 1000; // 默认超时时间为1000ms

    // 检查是否已初始化
    if (!m_bInitialized)
    {
        errorLog() << "Platform Manager has not been initialized."; // 输出错误日志
        return res;
    }

    // 检查连接状态
    if (!m_bConnect)
    {
        errorLog() << "writeData error: disconnected."; // 输出错误日志
        return res;
    }

    // 检查数据缓冲区是否为空
    if (buffer == nullptr)
    {
        errorLog() << "error: buffer is null."; // 输出错误日志
        return res;
    }

#ifdef Q_WS_QWS   // 如果定义了 Q_WS_QWS 宏，则调用 SPPM_WriteData 函数进行数据写入
    // 判断portHandle是否有效
    Q_ASSERT(portHandle != -1);

    // 调用 SPPM_WriteData 函数将数据写入蓝牙设备中
    int btRes = SPPM_WriteData(portHandle, timeout, dataLen, (unsigned char *)buffer);

    if (btRes >= 0)
    {
        // 如果写入成功，则打印出写入的字节数，并保存到返回值 res 中
        infoLog() << "SPPM_WriteData Success: " << btRes << " bytes written.";
        res = btRes;

        // 打印写入的数据内容
        infoLog() << "Data written: " << QByteArray(buffer, btRes).toHex();
    }
    else
    {
        // 如果写入失败，则打印出错误码以及转换后的错误信息
        warningLog() << "portHandle=" << portHandle << ", buffer len is " << strlen(buffer);
        warningLog() << "SPPM_WriteData Failure: " << btRes << ERR_ConvertErrorCodeToString(btRes);
    }
#else           // 否则，不进行任何操作。Q_UNUSED 宏是一个 Qt 提供的宏，用于避免编译器警告（unused parameter）
    Q_UNUSED(portHandle)
    Q_UNUSED(timeout)
#endif

    // 刷新标准输出缓冲区，确保输出结果正确。
    fflush(stdout);

    return res;
}
```

发现以下问题：
1. 当`btRes = 0`时（没有字节被写入），当前代码将其视为成功，不会尝试重新发送数据
2. 部分写入情况下没有处理机制，只返回实际写入的字节数
3. 连接状态检查仅依赖静态标志`m_bConnect`，没有动态检查机制

### 2.2 协议层发送函数分析

进一步分析了协议层的`LinkLayerProtocolJD::sendPackge`函数：

```cpp
void LinkLayerProtocolJD::sendPackge(const QByteArray &data, const LinkLayerProtocolJD::FrameInfo &frameInfo)
{
    // 使用 QMutexLocker 自动管理互斥锁的加锁和解锁
    QMutexLocker locker(&m_sendBufMutex);

    qint64 lOffset = 0;
    qint64 lBytesLeft = 0;

    QByteArray dataFrame;
    dataFrame.reserve(Protocol::FRAME_HEADER_LEN);
    dataFrame.fill( 0, Protocol::FRAME_HEADER_LEN );

    // 设置帧头、版本、帧序号等...

    // 添加数据内容
    dataFrame.append( data );  // 添加数据
    
    // 计算CRC校验值并添加
    // 添加帧尾

    lBytesLeft = dataFrame.size();

    debugLog() << "~~~~~~~~~~~~send data size : " << lBytesLeft ;
    int timeCount=0;

    while( lBytesLeft > 0 )
    {
        qint32 iBytesSend = lBytesLeft > MAX_FRAME_LEN ? MAX_FRAME_LEN : lBytesLeft;
        QByteArray dataSend = dataFrame.mid( lOffset,iBytesSend);

        if( -1 == m_pCom->write(dataSend))
        {
            emit sigRcvUploadDataRsp(100);
            break;
        }
        timeCount++;

        lBytesLeft -= iBytesSend;
        lOffset += iBytesSend;

        if( lBytesLeft > 0 )
        {
            // 这里使用TCP协议发送数据，如果发送过快，数据量过大，会导致发送缓冲区写爆，故添加延时
            QEventLoop eventloop;
            QTimer::singleShot(SEND_TIME_DELAY, &eventloop, SLOT(quit()));
            eventloop.exec();
        }
    }

    debugLog() << "Exiting sendPackge,timeCount:"<<timeCount;
}
```

发现以下问题：
1. 协议层仅检查`m_pCom->write`返回值是否为-1，未处理部分写入情况
2. 即使底层只写入部分数据，协议层代码也会认为已成功发送所有数据
3. 没有重试机制处理临时性写入失败

## 3. 发现的问题

综合分析，发现了以下关键问题：

1. **部分写入问题**：当请求写入大量数据但只有部分成功写入时，没有机制确保所有数据都被发送
2. **写入0字节的处理**：当`SPPM_WriteData`返回0时，没有重试机制
3. **连接状态检测不可靠**：仅依赖静态标志`m_bConnect`，缺乏动态检测机制
4. **错误处理不完善**：协议层和底层的错误处理机制不协调

## 4. 改进方案

### 4.1 底层写入函数改进

1. 增加重试机制，处理`SPPM_WriteData`返回0的情况
2. 实现循环写入，确保所有数据都被发送
3. 优化连接状态检测机制，采用"按需检查"策略：
   - 正常情况下使用静态标志`m_bConnect`进行快速检查
   - 只在写入数据返回0或错误时调用`checkConnectionState`进行详细检查
   - 平衡系统资源消耗与连接可靠性

### 4.2 协议层发送函数改进

1. 严格检查写入结果，确保协议数据的完整性
2. 当检测到部分写入时立即报告失败，而不是继续发送
3. 与底层错误处理机制协调

### 4.3 连接状态检测机制改进

1. 增加`checkConnectionState`方法，实时检查连接状态
2. 采用"按需检查"模式，减少资源消耗
3. 当检测到连接断开时自动更新状态标志并通知上层

## 5. 改进实现

### 5.1 改进的底层写入函数

```cpp
/*************************************************
函数名： checkConnectionState
输入参数： portHandle--端口句柄
输出参数： NULL
返回值： bool--连接正常返回true，异常返回false
功能： 检查蓝牙连接状态
*************************************************************/
bool Bluetooth::checkConnectionState(int portHandle)
{
    // 首先检查静态标志
    if (!m_bConnect)
    {
        return false;
    }
    
    // 检查端口句柄是否有效
    if (portHandle < 0)
    {
        return false;
    }
    
#ifdef Q_WS_QWS
    // 尝试获取连接状态
    SPP_Status_t sppStatus;
    int result = SPPM_QueryStatus(portHandle, &sppStatus);
    
    if (result < 0)
    {
        warningLog() << "SPPM_QueryStatus failed: " << result << ERR_ConvertErrorCodeToString(result);
        return false;
    }
    
    // 检查连接状态
    if (sppStatus.Connected != TRUE)
    {
        warningLog() << "SPP connection is not active";
        // 更新内部连接状态标志
        m_bConnect = false;
        // 触发连接状态变化信号
        emitConnectState(false);
        return false;
    }
#else
    Q_UNUSED(portHandle)
#endif

    return true;
}

int Bluetooth::writeData(int portHandle, const char *buffer, int dataLen)
{
    int res = -1; // 默认返回值为-1
    uint timeout = 1000; // 默认超时时间为1000ms
    const int maxRetryCount = 3; // 最大重试次数
    int retryCount = 0; // 当前重试次数
    int totalWritten = 0; // 总共写入的字节数
    const char* currentBuffer = buffer; // 当前写入位置的指针

    // 检查是否已初始化
    if (!m_bInitialized)
    {
        errorLog() << "Platform Manager has not been initialized."; // 输出错误日志
        return res;
    }

    // 使用静态标志进行快速检查，减少资源消耗
    if (!m_bConnect)
    {
        errorLog() << "writeData error: disconnected."; // 输出错误日志
        return res;
    }

    // 检查数据缓冲区是否为空
    if (buffer == nullptr)
    {
        errorLog() << "error: buffer is null."; // 输出错误日志
        return res;
    }

#ifdef Q_WS_QWS   // 如果定义了 Q_WS_QWS 宏，则调用 SPPM_WriteData 函数进行数据写入
    // 判断portHandle是否有效
    Q_ASSERT(portHandle != -1);

    // 循环写入所有数据
    while (totalWritten < dataLen)
    {
        int remainingBytes = dataLen - totalWritten;
        int btRes = 0;
        retryCount = 0; // 重置重试计数

        do {
            // 调用 SPPM_WriteData 函数将数据写入蓝牙设备中
            btRes = SPPM_WriteData(portHandle, timeout, remainingBytes, (unsigned char *)currentBuffer);

            if (btRes > 0)
            {
                // 如果写入成功且有数据写入，则更新计数和指针
                infoLog() << "SPPM_WriteData Success: " << btRes << " bytes written.";
                totalWritten += btRes;
                currentBuffer += btRes;
                break; // 成功写入数据，跳出重试循环
            }
            else if (btRes == 0)
            {
                // 没有数据被写入，这可能是连接问题，使用详细检查
                warningLog() << "SPPM_WriteData wrote 0 bytes, checking connection and retry " << (retryCount + 1) << "/" << maxRetryCount;
                
                // 在这里添加详细的连接状态检查
                if (!checkConnectionState(portHandle))
                {
                    errorLog() << "Connection lost detected during write retry";
                    return -1;
                }
                
                retryCount++;
                
                // 短暂延时后重试
                QThread::msleep(100);
            }
            else
            {
                // 如果写入失败，则打印出错误码以及转换后的错误信息
                warningLog() << "portHandle=" << portHandle << ", remaining bytes=" << remainingBytes;
                warningLog() << "SPPM_WriteData Failure: " << btRes << ERR_ConvertErrorCodeToString(btRes);
                
                // 检查连接状态是否需要更新
                checkConnectionState(portHandle);
                return -1; // 写入出错，直接返回错误
            }
        } while (retryCount < maxRetryCount && btRes == 0);

        // 如果所有重试都写入0字节，记录错误并返回
        if (btRes == 0 && retryCount >= maxRetryCount) {
            errorLog() << "Failed to write data after " << maxRetryCount << " retries";
            return -1;
        }
    }

    // 所有数据写入成功
    infoLog() << "All data written successfully. Total bytes: " << totalWritten;
    infoLog() << "Data written: " << QByteArray(buffer, totalWritten).toHex();
    res = totalWritten;
#else           // 否则，不进行任何操作。Q_UNUSED 宏是一个 Qt 提供的宏，用于避免编译器警告（unused parameter）
    Q_UNUSED(portHandle)
    Q_UNUSED(timeout)
#endif

    // 刷新标准输出缓冲区，确保输出结果正确。
    fflush(stdout);

    return res;
}
```

### 5.2 改进的协议层发送函数

```cpp
void LinkLayerProtocolJD::sendPackge(const QByteArray &data, const LinkLayerProtocolJD::FrameInfo &frameInfo)
{
    // 使用 QMutexLocker 自动管理互斥锁的加锁和解锁
    QMutexLocker locker(&m_sendBufMutex);

    qint64 lOffset = 0;
    qint64 lBytesLeft = 0;

    QByteArray dataFrame;
    dataFrame.reserve(Protocol::FRAME_HEADER_LEN);
    dataFrame.fill( 0, Protocol::FRAME_HEADER_LEN );

    // 设置帧头
    dataFrame[Protocol::POS_HEAD_EB_L] = Protocol::HEAD_EB;
    // ... 其他帧头设置 ...

    // 添加数据内容
    dataFrame.append( data );  // 添加数据
    
    // 计算CRC校验值
    // ... CRC计算和添加 ...

    // 添加帧尾
    dataFrame.append( Protocol::TAIL_03 ); // 添加帧尾

    lBytesLeft = dataFrame.size();

    debugLog() << "~~~~~~~~~~~~send data size : " << lBytesLeft ;
    int timeCount = 0;

    while( lBytesLeft > 0 )
    {
        qint32 iBytesSend = lBytesLeft > MAX_FRAME_LEN ? MAX_FRAME_LEN : lBytesLeft;
        QByteArray dataSend = dataFrame.mid( lOffset, iBytesSend);
        
        // 调用底层write函数，底层已经实现了重试机制
        int writeResult = m_pCom->write(dataSend);
        
        if (writeResult == dataSend.size())
        {
            // 写入完全成功，继续处理
            timeCount++;
            lBytesLeft -= writeResult;
            lOffset += writeResult;
        }
        else if (writeResult > 0 && writeResult < dataSend.size())
        {
            // 部分写入，认为任务失败
            errorLog() << "Partial write detected: wrote " << writeResult << " of " << dataSend.size() << " bytes. Failing the task.";
            emit sigRcvUploadDataRsp(100);
            return;
        }
        else
        {
            // 写入失败，直接返回错误
            errorLog() << "Write operation failed with result: " << writeResult;
            emit sigRcvUploadDataRsp(100);
            return;
        }

        if( lBytesLeft > 0 )
        {
            // 这里使用TCP协议发送数据，如果发送过快，数据量过大，会导致发送缓冲区写爆，故添加延时
            QEventLoop eventloop;
            QTimer::singleShot(SEND_TIME_DELAY, &eventloop, SLOT(quit()));
            eventloop.exec();
        }
    }

    debugLog() << "Exiting sendPackge, timeCount:" << timeCount;
}
```

## 6. 改进效果

### 6.1 主要改进点

1. **底层写入函数**：
   - 实现了可靠的写入机制，确保所有请求发送的数据都被写入
   - 添加了重试机制处理临时写入失败
   - 优化了连接状态检测，采用按需检查策略，减少资源消耗

2. **协议层发送函数**：
   - 严格检查写入结果，确保协议数据的完整性
   - 当检测到部分写入或失败时立即停止并报告错误

3. **连接状态检测**：
   - 实现了动态连接状态检测机制
   - 优化了检查时机，只在必要时进行详细检查
   - 当连接断开时自动更新状态标志并发出信号

### 6.2 可靠性提升

1. **数据完整性**：确保所有数据都被完整发送，避免数据丢失
2. **错误处理**：更精确地检测和报告错误，避免静默失败
3. **连接可靠性**：在关键时刻检测连接状态，及时发现连接问题
4. **性能优化**：减少不必要的连接状态查询，降低系统资源消耗

## 7. 时序图

### 7.1 改进前的写入流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Protocol as 协议层(LinkLayerProtocol)
    participant BT as 底层(Bluetooth)
    participant SPPM as 蓝牙驱动(SPPM)
    
    App->>Protocol: 发送数据请求
    Protocol->>Protocol: 构建数据帧
    loop 分片发送
        Protocol->>BT: write(dataSend)
        BT->>BT: 检查初始化状态
        BT->>BT: 检查静态连接标志(m_bConnect)
        BT->>SPPM: SPPM_WriteData()
        SPPM-->>BT: 返回写入结果(btRes)
        
        alt btRes >= 0
            BT-->>Protocol: 返回btRes (包括btRes=0)
            Protocol->>Protocol: 认为发送成功，更新偏移量
        else btRes < 0
            BT-->>Protocol: 返回-1
            Protocol->>Protocol: 中断发送
        end
    end
    Protocol-->>App: 发送完成
```

### 7.2 改进后的写入流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Protocol as 协议层(LinkLayerProtocol)
    participant BT as 底层(Bluetooth)
    participant SPPM as 蓝牙驱动(SPPM)
    
    App->>Protocol: 发送数据请求
    Protocol->>Protocol: 构建数据帧
    loop 分片发送
        Protocol->>BT: write(dataSend)
        BT->>BT: 检查初始化状态
        BT->>BT: 检查静态连接标志(m_bConnect)
        
        loop 确保所有数据都被发送
            BT->>SPPM: SPPM_WriteData(remainingBytes)
            SPPM-->>BT: 返回写入结果(btRes)
            
            alt btRes > 0
                BT->>BT: 更新发送进度，继续发送剩余数据
            else btRes = 0
                Note right of BT: 按需检查连接状态
                BT->>SPPM: SPPM_QueryStatus()
                SPPM-->>BT: 返回连接状态
                alt 连接正常
                    BT->>BT: 重试(最多maxRetryCount次)
                else 连接断开
                    BT-->>Protocol: 返回-1
                end
            else btRes < 0
                BT->>SPPM: SPPM_QueryStatus()
                SPPM-->>BT: 返回连接状态
                BT-->>Protocol: 返回-1
                Protocol-->>App: 报告发送失败
            end
        end
        
        alt 全部数据发送成功
            BT-->>Protocol: 返回totalWritten
            
            alt writeResult == dataSend.size()
                Protocol->>Protocol: 继续发送下一个分片
            else writeResult != dataSend.size()
                Protocol-->>App: 报告发送失败
            end
            
        else 发送失败
            Protocol-->>App: 报告发送失败
        end
    end
    Protocol-->>App: 发送完成
```

## 8. 总结

通过对蓝牙通信模块的底层写入函数和协议层发送函数进行改进，显著提高了数据传输的可靠性和稳定性。主要改进点包括：

1. 实现了可靠的数据写入机制，确保所有数据都被完整发送
2. 添加了合理的重试机制处理临时写入失败
3. 优化了连接状态检测策略，采用"按需检查"模式，在关键时刻进行详细检查
4. 完善了错误处理，避免静默失败
5. 平衡了系统性能与连接可靠性，减少不必要的资源消耗

这些改进使蓝牙通信模块能够更加可靠高效地完成数据传输任务，减少了因通信问题导致的系统故障，提高了整体系统的稳定性和性能。

## 9. 修改影响范围评估

### 9.1 代码影响范围

1. **直接修改的文件**:
   - `Z200/module/comm/bluetooth/bluetooth.cpp`
   - `Z200/module/comm/bluetooth/bluetooth.h`
   - `Z200/mobileAccess/appcomm/protocol/linklayerprotocoljd.cpp`

2. **影响的功能模块**:
   - 蓝牙通信基础层
   - 链路层协议处理
   - 应用层数据传输

### 9.2 潜在风险评估

1. **功能影响**:
   - **低风险**: 修改保持了原有API接口不变，调用方无需修改代码
   - **低风险**: 返回值语义与原实现兼容，不会破坏现有错误处理逻辑

2. **性能影响**:
   - **正面影响**: 对于正常情况，减少了不必要的连接状态检查，提高了性能
   - **中等影响**: 对于异常情况（如写入0字节），增加了重试逻辑，可能略微增加延迟
   - **低风险**: 重试次数和延迟时间设置合理，不会导致明显性能下降

3. **稳定性影响**:
   - **正面影响**: 提高了数据传输的完整性和可靠性
   - **正面影响**: 更精确地检测和处理错误情况
   - **正面影响**: 更可靠的连接状态管理

### 9.3 代码安全性分析

1. **边界情况处理**:
   - 所有可能的返回值都有明确处理逻辑
   - 添加了缓冲区有效性检查和端口句柄检查
   - 限制了最大重试次数，避免死循环

2. **资源管理**:
   - 没有新增的内存分配操作，不会引入内存泄漏
   - 使用固定的超时时间和重试间隔，资源消耗可控
   - 按需检查策略减少了不必要的系统调用

3. **并发安全**:
   - 没有修改锁机制，保持原有的并发控制
   - 状态更新操作（如更新m_bConnect）是原子的，不会导致不一致

### 9.4 实际应用影响

1. **用户体验**:
   - 应用会更准确地报告传输错误
   - 减少数据传输不完整的情况
   - 连接断开时会更快地通知上层应用

2. **业务流程**:
   - 改进后的错误处理可能导致更多任务失败（但这些是原本就应该失败的任务）
   - 上层应用需要处理这些失败情况，但现有的错误处理机制已足够应对

3. **兼容性**:
   - 完全兼容现有系统架构和调用方式
   - 无需修改任何依赖此模块的上层应用

### 9.5 最终结论

经过全面的代码审查和影响分析，认为这些改进是安全且必要的。它们解决了现有的可靠性问题，同时不会引入新的系统或业务错误。与可能的小幅度性能影响相比，获得的可靠性提升更为重要，特别是在处理关键数据传输时。

在实施过程中，建议在测试环境中充分验证这些变更，特别是针对大数据量传输和网络不稳定场景进行专项测试。 