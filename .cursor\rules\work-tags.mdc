---
description: 
globs: 
alwaysApply: false
---
# Work Tags Standards

This document outlines the standard work tags used in daily reports and their proper usage.

## Standard Work Tags

The following tags must be used in the specified format:

- `[开发]` - Software development, coding, and feature implementation
- `[测试]` - Testing activities (functional, regression, verification)
- `[维护]` - System maintenance, code maintenance, environment configuration
- `[文档]` - Documentation writing and updating
- `[设计]` - System design, solution design, architecture design
- `[分析]` - Problem analysis, code analysis, log analysis
- `[管理]` - Project management, task management, team coordination
- `[学习]` - Technical learning and research
- `[沟通]` - Team communication and cross-department collaboration
- `[支持]` - Technical support and troubleshooting

## Tag Format Requirements

1. Always use square brackets `[]` for tags
2. Place the tag after the task name
3. Never use other formats like `【】` or parentheses
4. Use only the standard tags listed above

## Tag Usage Example

```markdown
1. **蓝牙通信模块底层写入函数改进** [开发]
   - **时间:** [3.5小时]
   - **分析过程:** 详细描述...
   - **解决方案:** 详细描述...
```

## Common Tag Patterns

- Development tasks typically follow: [开发] → [测试] → [文档]
- Bug fixes usually include: [分析] → [开发] → [测试]
- Feature design starts with: [设计] before [开发]

## Tag Statistics

Work tags are used for time tracking and reporting. Daily reports automatically calculate time spent on each category in the "工作类型分布" section.

See [archived_reports/daily_report_2025_04.md](mdc:archived_reports/daily_report_2025_04.md) for examples of tag usage in actual reports.

